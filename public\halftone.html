<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Interactive Halftone Effect</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            margin: 0;
            overflow: hidden;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: #1a202c; /* Dark background for contrast */
            color: #e2e8f0; /* Light text color */
        }

        /* CSS Custom Properties (Variables) */
        :root {
            --dot-size: 0.25em;
            --line-color: #333;
            --line-contrast: 2000%;
            --photo-brightness: 80%;
            --photo-contrast: 150%;
            --photo-blur: 2px;
            --blend-mode: hard-light;
        }

        .canvas-container {
            position: relative;
            width: 80%; /* Responsive width */
            max-width: 800px; /* Max width for larger screens */
            aspect-ratio: 16 / 9; /* Maintain aspect ratio */
            background-color: #2d3748; /* Placeholder background */
            border-radius: 1rem; /* Rounded corners */
            overflow: hidden;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
            margin-bottom: 2rem;
        }

        .canvas {
            position: absolute;
            top: 0;
            left: 0;
            bottom: 0;
            right: 0;
            filter: contrast(var(--line-contrast));
            overflow: hidden;
        }

        .canvas::before {
            content: '';
            position: absolute;
            top: -50%;
            left: -50%;
            bottom: -50%;
            right: -50%;
            background: radial-gradient(circle at center, var(--line-color), #fff);
            background-size: var(--dot-size) var(--dot-size);
            transform: rotate(20deg);
        }

        .canvas img {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: cover;
            object-position: center;
            mix-blend-mode: var(--blend-mode);
            filter:
                grayscale(1)
                brightness(var(--photo-brightness))
                contrast(var(--photo-contrast))
                blur(var(--photo-blur));
        }

        .controls {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); /* Responsive grid */
            gap: 1rem;
            width: 80%;
            max-width: 800px;
            padding: 1.5rem;
            background-color: #2d3748;
            border-radius: 1rem;
            box-shadow: 0 10px 15px rgba(0, 0, 0, 0.3);
        }

        .control-group {
            display: flex;
            flex-direction: column;
            gap: 0.5rem;
        }

        .control-group label {
            font-weight: 600;
            font-size: 0.9rem;
            color: #a0aec0;
        }

        .control-group input[type="range"] {
            width: 100%;
            -webkit-appearance: none;
            height: 8px;
            background: #4a5568;
            border-radius: 5px;
            outline: none;
            opacity: 0.7;
            transition: opacity .2s;
        }

        .control-group input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #63b3ed;
            cursor: pointer;
        }

        .control-group input[type="range"]::-moz-range-thumb {
            width: 18px;
            height: 18px;
            border-radius: 50%;
            background: #63b3ed;
            cursor: pointer;
        }

        .control-group input[type="color"] {
            -webkit-appearance: none;
            -moz-appearance: none;
            appearance: none;
            width: 100%;
            height: 36px;
            border: none;
            border-radius: 0.5rem;
            background-color: #4a5568;
            cursor: pointer;
            padding: 0.25rem;
        }

        .control-group input[type="color"]::-webkit-color-swatch-wrapper {
            padding: 0;
        }

        .control-group input[type="color"]::-webkit-color-swatch {
            border: none;
            border-radius: 0.25rem;
        }

        .control-group select {
            width: 100%;
            padding: 0.5rem;
            border-radius: 0.5rem;
            background-color: #4a5568;
            color: #e2e8f0;
            border: 1px solid #4a5568;
            appearance: none; /* Remove default arrow */
            -webkit-appearance: none;
            -moz-appearance: none;
            background-image: url('data:image/svg+xml;charset=US-ASCII,%3Csvg%20xmlns%3D%22http%3A%2F%2Fwww.w3.org%2F2000%2Fsvg%22%20viewBox%3D%220%200%2020%2020%22%20fill%3D%22%23e2e8f0%22%3E%3Cpath%20fill-rule%3D%22evenodd%22%20d%3D%22M5.293%207.293a1%201%200%20011.414%200L10%2010.586l3.293-3.293a1%201%200%20111.414%201.414l-4%204a1%201%200%2001-1.414%200l-4-4a1%201%200%20010-1.414z%22%20clip-rule%3D%22evenodd%22%2F%3E%3C%2Fsvg%3E');
            background-repeat: no-repeat;
            background-position: right 0.75rem center;
            background-size: 1rem;
        }

        .upload-area {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 1rem;
            border: 2px dashed #63b3ed;
            border-radius: 0.5rem;
            cursor: pointer;
            text-align: center;
            transition: background-color 0.2s ease-in-out;
        }

        .upload-area:hover {
            background-color: #4a5568;
        }

        .upload-area input[type="file"] {
            display: none;
        }

        .upload-area p {
            margin: 0;
            font-size: 0.9rem;
            color: #a0aec0;
        }
    </style>
</head>
<body>
    <div class="canvas-container">
        <div class="canvas" id="halftoneCanvas">
            <img id="halftoneImage" src="https://images.unsplash.com/photo-1553514029-1318c9127859?cs=tinysrgb&ixid=MnwzMjM4NDZ8MHwxfHJhbmRvbXx8fHx8fHx8fDE2NTM5NDAxMTk&ixlib=rb-1.2.1&auto=format&auto=compress&w=1600" alt="Halftone effect base image" />
        </div>
    </div>

    <div class="controls">
        <div class="control-group">
            <label for="imageUpload">Upload Image</label>
            <div class="upload-area" id="uploadArea">
                <input type="file" id="imageUpload" accept="image/*">
                <p>Drag & drop or click to upload</p>
            </div>
        </div>

        <div class="control-group">
            <label for="dotSize">Dot Size (<span id="dotSizeValue">0.25</span>em)</label>
            <input type="range" id="dotSize" min="0.05" max="1" step="0.01" value="0.25">
        </div>

        <div class="control-group">
            <label for="lineColor">Line Color</label>
            <input type="color" id="lineColor" value="#333333">
        </div>

        <div class="control-group">
            <label for="lineContrast">Line Contrast (<span id="lineContrastValue">2000</span>%)</label>
            <input type="range" id="lineContrast" min="100" max="5000" step="10" value="2000">
        </div>

        <div class="control-group">
            <label for="photoBrightness">Photo Brightness (<span id="photoBrightnessValue">80</span>%)</label>
            <input type="range" id="photoBrightness" min="0" max="200" step="1" value="80">
        </div>

        <div class="control-group">
            <label for="photoContrast">Photo Contrast (<span id="photoContrastValue">150</span>%)</label>
            <input type="range" id="photoContrast" min="0" max="300" step="1" value="150">
        </div>

        <div class="control-group">
            <label for="photoBlur">Photo Blur (<span id="photoBlurValue">2</span>px)</label>
            <input type="range" id="photoBlur" min="0" max="10" step="0.1" value="2">
        </div>

        <div class="control-group">
            <label for="blendMode">Blend Mode</label>
            <select id="blendMode">
                <option value="normal">normal</option>
                <option value="multiply">multiply</option>
                <option value="screen">screen</option>
                <option value="overlay">overlay</option>
                <option value="darken">darken</option>
                <option value="lighten">lighten</option>
                <option value="color-dodge">color-dodge</option>
                <option value="color-burn">color-burn</option>
                <option value="hard-light" selected>hard-light</option>
                <option value="soft-light">soft-light</option>
                <option value="difference">difference</option>
                <option value="exclusion">exclusion</option>
                <option value="hue">hue</option>
                <option value="saturation">saturation</option>
                <option value="color">color</option>
                <option value="luminosity">luminosity</option>
            </select>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const root = document.documentElement;
            const halftoneImage = document.getElementById('halftoneImage');
            const imageUpload = document.getElementById('imageUpload');
            const uploadArea = document.getElementById('uploadArea');

            // Get all control elements
            const dotSizeInput = document.getElementById('dotSize');
            const lineColorInput = document.getElementById('lineColor');
            const lineContrastInput = document.getElementById('lineContrast');
            const photoBrightnessInput = document.getElementById('photoBrightness');
            const photoContrastInput = document.getElementById('photoContrast');
            const photoBlurInput = document.getElementById('photoBlur');
            const blendModeSelect = document.getElementById('blendMode');

            // Get value display spans
            const dotSizeValueSpan = document.getElementById('dotSizeValue');
            const lineContrastValueSpan = document.getElementById('lineContrastValue');
            const photoBrightnessValueSpan = document.getElementById('photoBrightnessValue');
            const photoContrastValueSpan = document.getElementById('photoContrastValue');
            const photoBlurValueSpan = document.getElementById('photoBlurValue');

            // Function to update CSS variables
            const updateCssVariables = () => {
                root.style.setProperty('--dot-size', `${dotSizeInput.value}em`);
                root.style.setProperty('--line-color', lineColorInput.value);
                root.style.setProperty('--line-contrast', `${lineContrastInput.value}%`);
                root.style.setProperty('--photo-brightness', `${photoBrightnessInput.value}%`);
                root.style.setProperty('--photo-contrast', `${photoContrastInput.value}%`);
                root.style.setProperty('--photo-blur', `${photoBlurInput.value}px`);
                root.style.setProperty('--blend-mode', blendModeSelect.value);

                // Update display values
                dotSizeValueSpan.textContent = dotSizeInput.value;
                lineContrastValueSpan.textContent = lineContrastInput.value;
                photoBrightnessValueSpan.textContent = photoBrightnessInput.value;
                photoContrastValueSpan.textContent = photoContrastInput.value;
                photoBlurValueSpan.textContent = photoBlurInput.value;
            };

            // Event listeners for sliders and dropdown
            dotSizeInput.addEventListener('input', updateCssVariables);
            lineColorInput.addEventListener('input', updateCssVariables);
            lineContrastInput.addEventListener('input', updateCssVariables);
            photoBrightnessInput.addEventListener('input', updateCssVariables);
            photoContrastInput.addEventListener('input', updateCssVariables);
            photoBlurInput.addEventListener('input', updateCssVariables);
            blendModeSelect.addEventListener('change', updateCssVariables);

            // Initialize variables on load
            updateCssVariables();

            // Image Upload Logic
            imageUpload.addEventListener('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        halftoneImage.src = e.target.result;
                    };
                    reader.readAsDataURL(file);
                }
            });

            // Drag and Drop functionality
            uploadArea.addEventListener('dragover', (e) => {
                e.preventDefault();
                uploadArea.classList.add('border-blue-500'); // Tailwind class for visual feedback
            });

            uploadArea.addEventListener('dragleave', () => {
                uploadArea.classList.remove('border-blue-500');
            });

            uploadArea.addEventListener('drop', (e) => {
                e.preventDefault();
                uploadArea.classList.remove('border-blue-500');
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    imageUpload.files = files; // Assign dropped files to input
                    imageUpload.dispatchEvent(new Event('change')); // Trigger change event
                }
            });

            // Click to upload
            uploadArea.addEventListener('click', () => {
                imageUpload.click();
            });
        });
    </script>
</body>
</html>
