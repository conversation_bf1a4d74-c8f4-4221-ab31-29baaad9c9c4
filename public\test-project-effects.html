<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Project Effects Save/Load</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563EB;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .result.error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .result.info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
        }
        h1 {
            color: #1F2937;
            text-align: center;
        }
        h3 {
            color: #374151;
            margin-bottom: 10px;
        }
        .instructions {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            color: #92400E;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🎨 Project Effects Save/Load Test</h1>
    
    <div class="instructions">
        <strong>Testing Instructions:</strong><br>
        1. Test the project save functionality with duotone and glitch effects<br>
        2. Verify effects are captured and stored in the database<br>
        3. Test project loading to ensure effects are restored<br>
        4. Check browser console and server logs for debugging information
    </div>

    <div class="test-section">
        <h3>Test 1: Open Design Editor for Project Testing</h3>
        <p>This will open the design editor where you can create a project with duotone and glitch effects.</p>
        <button class="test-button" onclick="openDesignEditorForProjectTesting()">Open Design Editor</button>
        <div id="designEditorResult" class="result info"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Open My Projects Page</h3>
        <p>This will open the my-projects page where you can load saved projects.</p>
        <button class="test-button" onclick="openMyProjectsPage()">Open My Projects</button>
        <div id="myProjectsResult" class="result info"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Check Function Availability</h3>
        <p>Verify that the duotone and glitch capture/restore functions are available.</p>
        <button class="test-button" onclick="checkFunctionAvailability()">Check Functions</button>
        <div id="functionResult" class="result"></div>
    </div>

    <script>
        function openDesignEditorForProjectTesting() {
            const result = document.getElementById('designEditorResult');
            
            const instructions = `
PROJECT TESTING INSTRUCTIONS:

1. Open Design Editor: http://localhost:3006/design-editor.html
2. Add some content (text or upload an image)
3. Apply DUOTONE effects:
   - Go to Image Effects section
   - Enable duotone checkbox
   - Set Color 1: #FF0000 (red)
   - Set Color 2: #00FF00 (green)
4. Apply GLITCH effects:
   - Enable color shift (set intensity to 2.0)
   - Enable wave deform (set amplitude to 20)
   - Enable pixel sort (set threshold to 0.3)
5. Save as project: Click "Save Project" button
6. Fill in project details and save
7. Check browser console for capture logs
8. Check server logs for API save logs

Expected Results:
- Console should show: "[SaveProject] 🎨 Captured duotone state: {enabled: true, color1: '#FF0000', ...}"
- Console should show: "[SaveProject] 🎨 Captured glitch state: {colorShift: {...}, waveDeform: {...}, ...}"
- Server should show: "[Projects] 🎨 DuotoneState received: {enabled: true, ...}"
- Server should show: "[Projects] 🎨 Saved duotoneState: {enabled: true, ...}"
            `;
            
            result.textContent = instructions;
            result.className = 'result info';
            
            // Open the design editor in a new tab
            window.open('http://localhost:3006/design-editor.html', '_blank');
        }

        function openMyProjectsPage() {
            const result = document.getElementById('myProjectsResult');
            
            const instructions = `
PROJECT LOADING INSTRUCTIONS:

1. Open My Projects: http://localhost:3006/my-projects.html
2. Find the project you just saved with effects
3. Click "Edit" to load the project
4. Check browser console for restoration logs
5. Verify that duotone and glitch effects are automatically applied

Expected Results:
- Console should show: "[ProjectLoader] 🎨 Found duotone state in project: {enabled: true, ...}"
- Console should show: "[ProjectLoader] 🎨 Found glitch state in project: {colorShift: {...}, ...}"
- Console should show: "[ProjectLoader] 🎨 Restoring duotone effects..."
- Console should show: "[ProjectLoader] 🎨 Restoring glitch effects..."
- Effects should be visually applied to the content
- UI controls should be restored to their previous states
            `;
            
            result.textContent = instructions;
            result.className = 'result info';
            
            // Open my projects in a new tab
            window.open('http://localhost:3006/my-projects.html', '_blank');
        }

        function checkFunctionAvailability() {
            const result = document.getElementById('functionResult');
            
            try {
                const functions = [
                    'captureDuotoneState',
                    'captureGlitchState',
                    'restoreDuotoneState',
                    'restoreGlitchState',
                    'generateDuotoneMatrix',
                    'applyDuotoneEffect',
                    'applyGlinchEffects'
                ];
                
                let report = 'Function Availability Report:\n\n';
                let allAvailable = true;
                
                functions.forEach(funcName => {
                    const available = typeof window[funcName] === 'function';
                    report += `${available ? '✅' : '❌'} ${funcName}: ${available ? 'Available' : 'Not Available'}\n`;
                    if (!available) allAvailable = false;
                });
                
                report += `\nOverall Status: ${allAvailable ? '✅ All functions available' : '❌ Some functions missing'}`;
                
                result.textContent = report;
                result.className = allAvailable ? 'result success' : 'result error';
                
            } catch (error) {
                result.textContent = 'Error checking functions: ' + error.message;
                result.className = 'result error';
            }
        }
    </script>
</body>
</html>
