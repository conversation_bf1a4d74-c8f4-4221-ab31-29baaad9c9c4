<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Template Save Functionality</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-button {
            background: #007cba;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .test-button:hover {
            background: #005a87;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            background: #f5f5f5;
            border-radius: 3px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
        }
        .success {
            background: #d4edda;
            color: #155724;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <h1>Template Save Functionality Test</h1>
    
    <div class="test-section">
        <h3>Test 1: Check if functions are globally accessible</h3>
        <button class="test-button" onclick="testGlobalFunctions()">Test Global Functions</button>
        <div id="globalFunctionsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Test duotone state capture</h3>
        <button class="test-button" onclick="testDuotoneCapture()">Test Duotone Capture</button>
        <div id="duotoneCaptureResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Test glitch state capture</h3>
        <button class="test-button" onclick="testGlitchCapture()">Test Glitch Capture</button>
        <div id="glitchCaptureResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Test duotone matrix generation</h3>
        <button class="test-button" onclick="testDuotoneMatrix()">Test Duotone Matrix</button>
        <div id="duotoneMatrixResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 5: Test complete template save simulation</h3>
        <button class="test-button" onclick="testTemplateSaveSimulation()">Simulate Template Save</button>
        <div id="templateSaveResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 6: Create test template with effects</h3>
        <p>This will open the design editor with instructions to test the complete workflow.</p>
        <button class="test-button" onclick="openDesignEditorForTesting()">Open Design Editor for Testing</button>
        <div id="designEditorResult" class="result"></div>
    </div>

    <script>
        function testGlobalFunctions() {
            const result = document.getElementById('globalFunctionsResult');
            const functions = [
                'generateDuotoneMatrix',
                'applyDuotoneEffect',
                'applyGlinchEffects',
                'captureDuotoneState',
                'captureGlitchState',
                'restoreDuotoneState',
                'restoreGlitchState'
            ];
            
            let output = 'Global Function Availability:\n';
            let allAvailable = true;
            
            functions.forEach(funcName => {
                const available = typeof window[funcName] === 'function';
                output += `${funcName}: ${available ? '✅ Available' : '❌ Not Available'}\n`;
                if (!available) allAvailable = false;
            });
            
            result.textContent = output;
            result.className = `result ${allAvailable ? 'success' : 'error'}`;
        }

        function testDuotoneCapture() {
            const result = document.getElementById('duotoneCaptureResult');
            
            try {
                // Create mock duotone controls
                createMockDuotoneControls();
                
                // Test the capture function
                if (typeof window.captureDuotoneState === 'function') {
                    const duotoneState = window.captureDuotoneState();
                    result.textContent = 'Duotone State Captured:\n' + JSON.stringify(duotoneState, null, 2);
                    result.className = 'result success';
                } else {
                    result.textContent = 'Error: captureDuotoneState function not available';
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
                result.className = 'result error';
            }
        }

        function testGlitchCapture() {
            const result = document.getElementById('glitchCaptureResult');
            
            try {
                // Create mock glitch controls
                createMockGlitchControls();
                
                // Test the capture function
                if (typeof window.captureGlitchState === 'function') {
                    const glitchState = window.captureGlitchState();
                    result.textContent = 'Glitch State Captured:\n' + JSON.stringify(glitchState, null, 2);
                    result.className = 'result success';
                } else {
                    result.textContent = 'Error: captureGlitchState function not available';
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
                result.className = 'result error';
            }
        }

        function testDuotoneMatrix() {
            const result = document.getElementById('duotoneMatrixResult');

            try {
                if (typeof window.generateDuotoneMatrix === 'function') {
                    const matrix = window.generateDuotoneMatrix('#3B82F6', '#EAB308');
                    result.textContent = 'Duotone Matrix Generated:\n' + matrix;
                    result.className = 'result success';
                } else {
                    result.textContent = 'Error: generateDuotoneMatrix function not available';
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
                result.className = 'result error';
            }
        }

        function testTemplateSaveSimulation() {
            const result = document.getElementById('templateSaveResult');

            try {
                // Create comprehensive mock controls
                createMockDuotoneControls();
                createMockGlitchControls();

                // Test both capture functions
                const duotoneState = window.captureDuotoneState ? window.captureDuotoneState() : null;
                const glitchState = window.captureGlitchState ? window.captureGlitchState() : null;

                // Simulate what would be sent to the server
                const templateData = {
                    name: 'Test Template',
                    duotoneState: duotoneState,
                    glitchState: glitchState,
                    adminData: {
                        duotoneState: duotoneState,
                        glitchState: glitchState
                    }
                };

                result.textContent = 'Template Save Simulation:\n' + JSON.stringify(templateData, null, 2);
                result.className = 'result success';

            } catch (error) {
                result.textContent = 'Error: ' + error.message;
                result.className = 'result error';
            }
        }

        function openDesignEditorForTesting() {
            const result = document.getElementById('designEditorResult');

            const instructions = `
TESTING INSTRUCTIONS:

1. Open Design Editor: http://localhost:3006/design-editor.html
2. Add some content (text or upload an image)
3. Apply DUOTONE effects:
   - Go to Image Effects section
   - Enable duotone checkbox
   - Set Color 1: #3B82F6 (blue)
   - Set Color 2: #EAB308 (yellow)
4. Apply GLITCH effects:
   - Enable palette reduction
   - Enable color shift (set intensity to 1.5)
   - Enable wave deform (set amplitude to 15)
5. Save as template: Click "Save as Inspiration"
6. Check admin interface: http://localhost:3006/admin-inspirations.html
7. Find your template and click "Edit"
8. Verify: duotoneEffects and glitchEffects textareas should contain JSON data

Expected Results:
- Duotone textarea should show: {"enabled": true, "color1": "#3B82F6", "color2": "#EAB308", "matrix": "..."}
- Glitch textarea should show: {"paletteReduction": {...}, "colorShift": {...}, "waveDeform": {...}}
            `;

            result.textContent = instructions;
            result.className = 'result';

            // Open the design editor in a new tab
            window.open('http://localhost:3006/design-editor.html', '_blank');
        }

        function createMockDuotoneControls() {
            // Remove existing mock controls
            const existing = document.querySelectorAll('.mock-control');
            existing.forEach(el => el.remove());
            
            // Create mock duotone controls
            const duotoneEnabled = document.createElement('input');
            duotoneEnabled.type = 'checkbox';
            duotoneEnabled.id = 'duotoneEnabled';
            duotoneEnabled.checked = true;
            duotoneEnabled.className = 'mock-control';
            duotoneEnabled.style.display = 'none';
            document.body.appendChild(duotoneEnabled);
            
            const duotoneColor1 = document.createElement('input');
            duotoneColor1.type = 'color';
            duotoneColor1.id = 'duotoneColor1';
            duotoneColor1.value = '#3B82F6';
            duotoneColor1.className = 'mock-control';
            duotoneColor1.style.display = 'none';
            document.body.appendChild(duotoneColor1);
            
            const duotoneColor2 = document.createElement('input');
            duotoneColor2.type = 'color';
            duotoneColor2.id = 'duotoneColor2';
            duotoneColor2.value = '#EAB308';
            duotoneColor2.className = 'mock-control';
            duotoneColor2.style.display = 'none';
            document.body.appendChild(duotoneColor2);
        }

        function createMockGlitchControls() {
            // Create mock glitch controls
            const paletteReductionEnabled = document.createElement('input');
            paletteReductionEnabled.type = 'checkbox';
            paletteReductionEnabled.id = 'paletteReductionEnabled';
            paletteReductionEnabled.checked = true;
            paletteReductionEnabled.className = 'mock-control';
            paletteReductionEnabled.style.display = 'none';
            document.body.appendChild(paletteReductionEnabled);
            
            const paletteName = document.createElement('select');
            paletteName.id = 'paletteName';
            paletteName.className = 'mock-control';
            paletteName.style.display = 'none';
            const option = document.createElement('option');
            option.value = 'desert';
            option.selected = true;
            paletteName.appendChild(option);
            document.body.appendChild(paletteName);
            
            const colorShiftEnabled = document.createElement('input');
            colorShiftEnabled.type = 'checkbox';
            colorShiftEnabled.id = 'colorShiftEnabled';
            colorShiftEnabled.checked = true;
            colorShiftEnabled.className = 'mock-control';
            colorShiftEnabled.style.display = 'none';
            document.body.appendChild(colorShiftEnabled);
            
            const colorShiftIntensity = document.createElement('input');
            colorShiftIntensity.type = 'range';
            colorShiftIntensity.id = 'colorShiftIntensity';
            colorShiftIntensity.value = '1.5';
            colorShiftIntensity.className = 'mock-control';
            colorShiftIntensity.style.display = 'none';
            document.body.appendChild(colorShiftIntensity);
        }
    </script>
</body>
</html>
