 // Global font map - moved outside function so it's accessible everywhere
const fontMap = {
    'Acme': {
        regular: '/fonts/Acme-Regular.ttf'
    },
    'Afacad Flux': {
        regular: '/fonts/AfacadFlux.ttf'
    },
    'Allen Sans': {
        bold: '/fonts/AllenSans-Bold.ttf',
        regular: '/fonts/AllenSans-Regular.ttf'
    },
    'Alumni Sans SC': {
        regular: '/fonts/AlumniSansSC.ttf',
        bold: '/fonts/AlumniSansSC-Bold.ttf',
        boldItalic: '/fonts/AlumniSansSC-BoldItalic.ttf',
        italic: '/fonts/AlumniSansSC-Italic.ttf'
    },
    'Amatic SC': {
        bold: '/fonts/AmaticSC-Bold.ttf',
        regular: '/fonts/AmaticSC-Regular.ttf'
    },
    'Anek Latin': {
        regular: '/fonts/AnekLatin.ttf',
        bold: '/fonts/AnekLatin-Bold.ttf',
    },
    '<PERSON>': {
        regular: '/fonts/Anton-Regular.ttf'
    },
    'Are You Serious': {
        regular: '/fonts/AreYouSerious-Regular.ttf'
    },
    'Arial': {
        bold: '/fonts/ArialBold.ttf',
        regular: '/fonts/Arial.ttf',
        boldItalic: '/fonts/ArialBoldItalic.ttf',
        italic: '/fonts/ArialItalic.ttf'
    },
    'Audiowide': {
        regular: '/fonts/Audiowide-Regular.ttf'
    },
    'Aventi': {
        bold: '/fonts/AventiBold.ttf'
    },
    'Averia Gruesa Libre': {
        regular: '/fonts/AveriaGruesaLibre-Regular.ttf'
    },
    'Averia Sans Libre': {
        bold: '/fonts/AveriaSansLibre-Bold.ttf',
        boldItalic: '/fonts/AveriaSansLibre-BoldItalic.ttf',
        italic: '/fonts/AveriaSansLibre-Italic.ttf',
        regular: '/fonts/AveriaSansLibre-Regular.ttf'
    },
    'Averia Serif Libre': {
        bold: '/fonts/AveriaSerifLibre-Bold.ttf',
        boldItalic: '/fonts/AveriaSerifLibre-BoldItalic.ttf',
        italic: '/fonts/AveriaSerifLibre-Italic.ttf',
        regular: '/fonts/AveriaSerifLibre-Regular.ttf'
    },
    'Bacasime Antique': {
        regular: '/fonts/BacasimeAntique-Regular.ttf'
    },
    'Badeen Display': {
        regular: '/fonts/BadeenDisplay-Regular.ttf'
    },
    'Bagel Fat One': {
        regular: '/fonts/BagelFatOne-Regular.ttf'
    },
    'Bangers': {
        regular: '/fonts/Bangers-Regular.ttf'
    },
    'Bebas Neue': {
        regular: '/fonts/BebasNeue-Regular.ttf'
    },
    'Beirut': {
        ht: '/fonts/Beirut[ht].ttf'
    },
    'Belanosima': {
        bold: '/fonts/Belanosima-Bold.ttf',
        regular: '/fonts/Belanosima-Regular.ttf'
    },
    'Beth Ellen': {
        regular: '/fonts/BethEllen-Regular.ttf'
    },
    'Bevan': {
        italic: '/fonts/Bevan-Italic.ttf',
        regular: '/fonts/Bevan-Regular.ttf'
    },
    'Bigelow Rules': {
        regular: '/fonts/BigelowRules-Regular.ttf'
    },
    'Birthstone Bounce': {
        regular: '/fonts/BirthstoneBounce-Regular.ttf'
    },
    'Bladerounded': {
        regular: '/fonts/Bladerounded-Regular.ttf'
    },
    'Block Mono': {
        bold: '/fonts/BlockMono-Bold.ttf',
        regular: '/fonts/BlockMono-Regular.ttf'
    },
    'Bodoni Moda': {
        regular: '/fonts/BodoniModa.ttf',
        '9ptBold': '/fonts/BodoniModa_9pt-Bold.ttf',
        ht: '/fonts/BodoniModaht.ttf',
        italic: '/fonts/BodoniModa-Italic.ttf'
    },
    'Bonbon': {
        regular: '/fonts/Bonbon-Regular.ttf'
    },
    'Boogaloo': {
        regular: '/fonts/Boogaloo-Regular.ttf'
    },
    'Bowlby One SC': {
        regular: '/fonts/BowlbyOneSC-Regular.ttf'
    },
    'Brawler': {
        bold: '/fonts/Brawler-Bold.ttf',
        regular: '/fonts/Brawler-Regular.ttf'
    },
    'Bruno Ace SC': {
        regular: '/fonts/BrunoAceSC-Regular.ttf'
    },
    'Bubblegum Sans': {
        regular: '/fonts/BubblegumSans-Regular.ttf'
    },
    'Bungee': {
        regular: '/fonts/Bungee-Regular.ttf'
    },
    'Caesar Dressing': {
        regular: '/fonts/CaesarDressing-Regular.ttf'
    },
    'Cagliostro': {
        regular: '/fonts/Cagliostro-Regular.ttf'
    },
    'Cal Sans': {
        regular: '/fonts/CalSans-Regular.ttf'
    },
    'Carter One': {
        regular: '/fonts/CarterOne-Regular.ttf'
    },
    'Castoro Titling': {
        regular: '/fonts/CastoroTitling-Regular.ttf'
    },
    'Caveat Brush': {
        regular: '/fonts/CaveatBrush-Regular.ttf'
    },
    'Changa': {
        regular: '/fonts/Changa.ttf',
        bold: '/fonts/Changa-Bold.ttf'
    },
    'Chango': {
        regular: '/fonts/Chango-Regular.ttf'
    },
    'Chau Philomene One': {
        italic: '/fonts/ChauPhilomeneOne-Italic.ttf',
        regular: '/fonts/ChauPhilomeneOne-Regular.ttf'
    },
    'Cherry Bomb One': {
        regular: '/fonts/CherryBombOne-Regular.ttf'
    },
    'Chewy': {
        regular: '/fonts/Chewy-Regular.ttf'
    },
    'Chicle': {
        regular: '/fonts/Chicle-Regular.ttf'
    },
    'Cinzel Decorative': {
        bold: '/fonts/CinzelDecorative-Bold.ttf',
        regular: '/fonts/CinzelDecorative-Regular.ttf'
    },
    'Climate Crisis': {
        regular: '/fonts/ClimateCrisis-Regular.ttf'
    },
    'Comforter Brush': {
        regular: '/fonts/ComforterBrush-Regular.ttf'
    },
    'Comforter': {
        regular: '/fonts/Comforter-Regular.ttf'
    },
    'Comic Sans MS': {
        regular: '/fonts/ComicSansMS.ttf',
        bold: '/fonts/ComicSansMSBold.ttf'
    },
    'Concert One': {
        regular: '/fonts/ConcertOne-Regular.ttf'
    },
    'Cookie': {
        regular: '/fonts/Cookie-Regular.ttf'
    },
    'Copilot': {
        regular: '/fonts/copilot.ttf'
    },
    'Copse': {
        regular: '/fonts/Copse-Regular.ttf'
    },
    'Corben': {
        bold: '/fonts/Corben-Bold.ttf',
        regular: '/fonts/Corben-Regular.ttf'
    },
    'Courier New': {
        regular: '/fonts/CourierNew.ttf',
        bold: '/fonts/CourierNewBold.ttf',
        boldItalic: '/fonts/CourierNewBoldItalic.ttf',
        italic: '/fonts/CourierNewItalic.ttf'
    },
    'Creepster': {
        regular: '/fonts/Creepster-Regular.ttf'
    },
    'Croissant One': {
        regular: '/fonts/CroissantOne-Regular.ttf'
    },
    'Crushed': {
        regular: '/fonts/Crushed-Regular.ttf'
    },
    'Dancing Script': {
        regular: '/fonts/DancingScript.ttf',
        bold: '/fonts/DancingScript-Bold.ttf'
    },
    'Danfo': {
        claw: '/fonts/Danfo-Claw.ttf',
        comb: '/fonts/Danfo-Comb.ttf',
        regular: '/fonts/Danfo-Regular.ttf'
    },
    'Dangrek': {
        regular: '/fonts/Dangrek-Regular.ttf'
    },
    'Dashicons': {
        regular: '/fonts/dashicons.ttf'
    },
    'Days One': {
        regular: '/fonts/DaysOne-Regular.ttf'
    },
    'Delicious Handrawn': {
        regular: '/fonts/DeliciousHandrawn-Regular.ttf'
    },
    'Denk One': {
        regular: '/fonts/DenkOne-Regular.ttf'
    },
    'Diplomata SC': {
        regular: '/fonts/DiplomataSC-Regular.ttf'
    },
    'Durendal And Oliphant': {
        regular: '/fonts/DurendalAndOliphantRegular.ttf'
    },
    'DynaPuff': {
        regular: '/fonts/DynaPuff.ttf',
        condensedBold: '/fonts/DynaPuff_Condensed-Bold.ttf',
        condensedRegular: '/fonts/DynaPuff_Condensed-Regular.ttf',
        bold: '/fonts/DynaPuff-Bold.ttf'
    },
    'East Sea Dokdo': {
        regular: '/fonts/EastSeaDokdo-Regular.ttf'
    },
    'EB Garamond': {
        regular: '/fonts/EBGaramond.ttf',
        bold: '/fonts/EBGaramond-Bold.ttf',
        boldItalic: '/fonts/EBGaramond-BoldItalic.ttf',
        italic: '/fonts/EBGaramond-Italic.ttf'
    },
    'Encode Sans SC': {
        regular: '/fonts/EncodeSansSC.ttf',
        bold: '/fonts/EncodeSansSC-Bold.ttf',
    },
    'Englebert': {
        regular: '/fonts/Englebert-Regular.ttf'
    },
    'Exile': {
        regular: '/fonts/Exile-Regular.ttf'
    },
    'Exo 2': {
        regular: '/fonts/Exo2.ttf',
        bold: '/fonts/Exo2-Bold.ttf',
        boldItalic: '/fonts/Exo2-BoldItalic.ttf',
        italic: '/fonts/Exo2-Italic.ttf'
    },
    'Festive': {
        regular: '/fonts/Festive-Regular.ttf'
    },
    'Flavors': {
        regular: '/fonts/Flavors-Regular.ttf'
    },
    'Fontdiner Swanky': {
        regular: '/fonts/FontdinerSwanky-Regular.ttf'
    },
    'Freckle Face': {
        regular: '/fonts/FreckleFace-Regular.ttf'
    },
    'Freeman': {
        regular: '/fonts/Freeman-Regular.ttf'
    },
    'Frijole': {
        regular: '/fonts/Frijole-Regular.ttf'
    },
    'Fugaz One': {
        regular: '/fonts/FugazOne-Regular.ttf'
    },
    'Funnel Display': {
        bold: '/fonts/FunnelDisplay-Bold.ttf',
        regular: '/fonts/FunnelDisplay-Regular.ttf'
    },
    'Garamond': {
        regular: '/fonts/Garamond.ttf',
        italic: '/fonts/Garamond-Italic.ttf'
    },
    'Georgia': {
        regular: '/fonts/Georgia.ttf',
        bold: '/fonts/GeorgiaBold.ttf',
        boldItalic: '/fonts/GeorgiaBoldItalic.ttf',
        italic: '/fonts/GeorgiaItalic.ttf'
    },
    'Gloock': {
        regular: '/fonts/Gloock-Regular.ttf'
    },
    'Gloria Hallelujah': {
        regular: '/fonts/GloriaHallelujah-Regular.ttf'
    },
    'Gluten': {
        regular: '/fonts/Gluten.ttf',
        bold: '/fonts/Gluten-Bold.ttf'
    },
    'Goblin One': {
        regular: '/fonts/GoblinOne-Regular.ttf'
    },
    'Gochi Hand': {
        regular: '/fonts/GochiHand-Regular.ttf'
    },
    'Goli': {
        bold: '/fonts/Goli-Bold.ttf',
        vf: '/fonts/GoliVF.ttf',
        regular: '/fonts/Goli-Regular.ttf'
    },
    'Gorditas': {
        bold: '/fonts/Gorditas-Bold.ttf',
        regular: '/fonts/Gorditas-Regular.ttf'
    },
    'Gravitas One': {
        regular: '/fonts/GravitasOne-Regular.ttf'
    },
    'Gully': {
        bold: '/fonts/Gully-Bold.ttf',
        vf: '/fonts/GullyVF.ttf'
    },
    'Gyrochrome VF': {
        regular: '/fonts/GyrochromeVF.ttf'
    },
    'Hammersmith One': {
        regular: '/fonts/HammersmithOne-Regular.ttf'
    },
    'Hanalei Fill': {
        regular: '/fonts/HanaleiFill-Regular.ttf'
    },
    'Henny Penny': {
        regular: '/fonts/HennyPenny-Regular.ttf'
    },
    'Holtwood One SC': {
        regular: '/fonts/HoltwoodOneSC-Regular.ttf'
    },
    'Homemade Apple': {
        regular: '/fonts/HomemadeApple-Regular.ttf'
    },
    'Hyrax': {
        regular: '/fonts/hyrax.ttf'
    },
    'IBM Plex Serif': {
        bold: '/fonts/IBMPlexSerif-Bold.ttf',
        boldItalic: '/fonts/IBMPlexSerif-BoldItalic.ttf',
        italic: '/fonts/IBMPlexSerif-Italic.ttf',
        regular: '/fonts/IBMPlexSerif-Regular.ttf'
    },
    'IM Fell DW Pica': {
        italic: '/fonts/IMFellDWPica-Italic.ttf',
        regular: '/fonts/IMFellDWPica-Regular.ttf'
    },
    'IM Fel Pica': {
        italic: '/fonts/IMFelPica-Italic.ttf',
        regular: '/fonts/IMFelPica-Regular.ttf'
    },
    'Impact': {
        regular: '/fonts/Impact.ttf'
    },
    'Inconsolata': {
        bold: '/fonts/Inconsolata-Bold.ttf',
        regular: '/fonts/Inconsolata-Regular.ttf'
    },
    'Inspiration': {
        regular: '/fonts/Inspiration-Regular.ttf'
    },
    'Inter': {
        regular: '/fonts/Inter.ttf',
        ht: '/fonts/Interht.ttf',
        italic: '/fonts/Inter-Italic.ttf',
        italicht: '/fonts/Inter-Italicht.ttf'
    },
    'Irish Grover': {
        regular: '/fonts/IrishGrover-Regular.ttf'
    },
    'Jolly Lodger': {
        regular: '/fonts/JollyLodger-Regular.ttf'
    },
    'Jonova': {
        bold: '/fonts/Jonova-Bold.ttf',
        boldItalic: '/fonts/Jonova-BoldItalic.ttf',
        italic: '/fonts/Jonova-Italic.ttf',
        regular: '/fonts/Jonova-Regular.ttf'
    },
    'Jua': {
        regular: '/fonts/Jua-Regular.ttf'
    },
    'Just Another Hand': {
        regular: '/fonts/JustAnotherHand-Regular.ttf'
    },
    'Kablammo': {
        regularMorF: '/fonts/Kablammo-Regular-MORF.ttf'
    },
    'Karantina': {
        bold: '/fonts/Karantina-Bold.ttf',
        regular: '/fonts/Karantina-Regular.ttf'
    },
    'Karla': {
        bold: '/fonts/Karla-Bold.ttf',
        boldItalic: '/fonts/Karla-BoldItalic.ttf',
        italic: '/fonts/Karla-Italic.ttf',
        regular: '/fonts/Karla-Regular.ttf'
    },
    'Kaushan Script': {
        regular: '/fonts/KaushanScript-Regular.ttf'
    },
    'Kranky': {
        regular: '/fonts/Kranky-Regular.ttf'
    },
    'Kristi': {
        regular: '/fonts/Kristi-Regular.ttf'
    },
    'Lakki Reddy': {
        regular: '/fonts/LakkiReddy-Regular.ttf'
    },
    'Lato': {
        bold: '/fonts/Lato-Bold.ttf',
        boldItalic: '/fonts/Lato-BoldItalic.ttf',
        italic: '/fonts/Lato-Italic.ttf',
        regular: '/fonts/Lato-Regular.ttf'
    },
    'League Gothic': {
        condensedRegular: '/fonts/LeagueGothic_Condensed-Regular.ttf',
        regular: '/fonts/LeagueGothic-Regular.ttf'
    },
    'Lexend Giga': {
        regular: '/fonts/LexendGiga.ttf',
        bold: '/fonts/LexendGiga-Bold.ttf'
    },
    'Lexend Tera': {
        bold: '/fonts/LexendTera-Bold.ttf',
        regular: '/fonts/LexendTera-Regular.ttf'
    },
    'Libre Baskerville': {
        bold: '/fonts/LibreBaskerville-Bold.ttf',
        italic: '/fonts/LibreBaskerville-Italic.ttf',
        regular: '/fonts/LibreBaskerville-Regular.ttf'
    },
    'Literata': {
        regular: '/fonts/Literata.ttf',
        bold: '/fonts/Literata-Bold.ttf',
        boldItalic: '/fonts/Literata-BoldItalic.ttf',
        italic: '/fonts/Literata-Italic.ttf'
    },
    'Lobster': {
        regular: '/fonts/Lobster-Regular.ttf'
    },
    'Londrina Solid': {
        regular: '/fonts/LondrinaSolid-Regular.ttf'
    },
    'Lora': {
        regular: '/fonts/Lora.ttf',
        bold: '/fonts/Lora-Bold.ttf',
        boldItalic: '/fonts/Lora-BoldItalic.ttf',
        italic: '/fonts/Lora-Italic.ttf'
    },
    'Loved by the King': {
        regular: '/fonts/LovedbytheKing-Regular.ttf'
    },
    'Love Ya Like A Sister': {
        regular: '/fonts/LoveYaLikeASister-Regular.ttf'
    },
    'LT Crow': {
        bold: '/fonts/LTCrow-Bold.ttf',
        regular: '/fonts/LTCrow-Regular.ttf'
    },
    'LT Hoodlum': {
        regular: '/fonts/LTHoodlum-Regular.ttf'
    },
    'LT Hoop': {
        bold: '/fonts/LTHoop-Bold.ttf',
        regular: '/fonts/LTHoop-Regular.ttf'
    },
    'LT Humor': {
        bold: '/fonts/LTHumor-Bold.ttf',
        boldItalic: '/fonts/LTHumor-BoldItalic.ttf',
        italic: '/fonts/LTHumor-Italic.ttf',
        regular: '/fonts/LTHumor-Regular.ttf'
    },
    'LT Museum': {
        bold: '/fonts/LTMuseum-Bold.ttf',
        boldItalic: '/fonts/LTMuseum-BoldItalic.ttf'
    },
    'LT Overflux': {
        regular: '/fonts/LTOverflux-Regular.ttf'
    },
    'LT Turbo': {
        regular: '/fonts/LTTurbo-Regular.ttf'
    },
    'Luckiest Guy': {
        regular: '/fonts/LuckiestGuy-Regular.ttf'
    },
    'Macondo': {
        regular: '/fonts/Macondo-Regular.ttf'
    },
    'Mara Pfont': {
        regular: '/fonts/MaraPfont.ttf'
    },
    'Marcellus SC': {
        regular: '/fonts/MarcellusSC-Regular.ttf'
    },
    'Matemasie': {
        regular: '/fonts/Matemasie-Regular.ttf'
    },
    'Merriweather': {
        wdth: '/fonts/Merriweatherwdth.ttf'
    },
    'Midnight Letters': {
        regular: '/fonts/MidnightLetters.ttf',
        italic: '/fonts/MidnightLettersItalic.ttf'
    },
    'Modak': {
        regular: '/fonts/Modak-Regular.ttf'
    },
    'Moloko Font': {
        regular: '/fonts/molokofont.ttf'
    },
    'Monoton': {
        regular: '/fonts/Monoton-Regular.ttf'
    },
    'Moonlit Flow': {
        regular: '/fonts/MoonlitFlow.ttf',
        italic: '/fonts/MoonlitFlowItalic.ttf'
    },
    'Mouse Memoirs': {
        regular: '/fonts/MouseMemoirs-Regular.ttf'
    },
    'Mystery Quest': {
        regular: '/fonts/MysteryQuest-Regular.ttf'
    },
    'Nerko One': {
        regular: '/fonts/NerkoOne-Regular.ttf'
    },
    'Norican': {
        regular: '/fonts/Norican-Regular.ttf'
    },
    'Nunito Sans': {
        regular: '/fonts/NunitoSans.ttf',
        italic: '/fonts/NunitoSans-Italic.ttf'
    },
    'Ode Erik': {
        regular: '/fonts/Ode-Erik.ttf'
    },
    'Oi': {
        regular: '/fonts/Oi-Regular.ttf'
    },
    'Ole': {
        regular: '/fonts/Ole-Regular.ttf'
    },
    'Orbitron': {
        bold: '/fonts/Orbitron-Bold.ttf',
        regular: '/fonts/Orbitron-Regular.ttf'
    },
    'Original Surfer': {
        regular: '/fonts/OriginalSurfer-Regular.ttf'
    },
    'Oswald': {
        regular: '/fonts/Oswald-.ttf', // Keeping as is if no other weight given
        bold: '/fonts/Oswald-Bold.ttf'
    },
    'Outfit': {
        regular: '/fonts/Outfit-.ttf', // Keeping as is if no other weight given
        bold: '/fonts/Outfit-Bold.ttf'
    },
    'Pacifico': {
        regular: '/fonts/Pacifico-Regular.ttf'
    },
    'Patua One': {
        regular: '/fonts/PatuaOne-Regular.ttf'
    },
    'Peralta': {
        regular: '/fonts/Peralta-Regular.ttf'
    },
    'Permanent Marker': {
        regular: '/fonts/PermanentMarker-Regular.ttf'
    },
    'Piedra': {
        regular: '/fonts/Piedra-Regular.ttf'
    },
    'Playfair Display': {
        regular: '/fonts/PlayfairDisplay-.ttf', // Keeping as is if no other weight given
        bold: '/fonts/PlayfairDisplay-Bold.ttf',
        boldItalic: '/fonts/PlayfairDisplay-BoldItalic.ttf',
        italic: '/fonts/PlayfairDisplay-Italic.ttf'
    },
    'Playwrite IT Moderna': {
        regular: '/fonts/PlaywriteITModerna-Regular.ttf'
    },
    'Plus Jakarta Sans': {
        bold: '/fonts/PlusJakartaSans-Bold.ttf',
        boldItalic: '/fonts/PlusJakartaSans-BoldItalic.ttf',
        italic: '/fonts/PlusJakartaSans-Italic.ttf',
        regular: '/fonts/PlusJakartaSans-Regular.ttf'
    },
    'Poppins': {
        regular: '/fonts/Poppins-Regular.ttf'
    },
    'Potta One': {
        regular: '/fonts/PottaOne-Regular.ttf'
    },
    'Princess Sofia': {
        regular: '/fonts/PrincessSofia-Regular.ttf'
    },
    'Protest Riot': {
        regular: '/fonts/ProtestRiot-Regular.ttf'
    },
    'Purple Purse': {
        regular: '/fonts/PurplePurse-Regular.ttf'
    },
    'Raleway': {
        regular: '/fonts/Raleway-.ttf', // Keeping as is if no other weight given
        bold: '/fonts/Raleway-Bold.ttf',
        boldItalic: '/fonts/Raleway-BoldItalic.ttf',
        italic: '/fonts/Raleway-Italic.ttf'
    },
    'Rammetto One': {
        regular: '/fonts/RammentoOne-Regular.ttf'
    },
    'Ranchers': {
        regular: '/fonts/Ranchers-Regular.ttf'
    },
    'Ribeye Marrow': {
        regular: '/fonts/RibeyeMarrow-Regular.ttf'
    },
    'Righteous': {
        regular: '/fonts/Righteous-Regular.ttf'
    },
    'Road Rage': {
        regular: '/fonts/RoadRage-Regular.ttf'
    },
    'Roboto': {
        boldItalic: '/fonts/Roboto-BoldItalic.ttf',
        italic: '/fonts/Roboto-Italic.ttf',
        regular: '/fonts/Roboto-Regular.ttf'
    },
    'Roboto Slab': {
        bold: '/fonts/RobotoSlab-Bold.ttf',
        regular: '/fonts/RobotoSlab-Regular.ttf'
    },
    'Rock Salt': {
        regular: '/fonts/RockSalt-Regular.ttf'
    },
    'Rokkitt': {
        regular: '/fonts/Rokkitt.ttf',
        bold: '/fonts/Rokkitt-Bold.ttf',
        boldItalic: '/fonts/Rokkitt-BoldItalic.ttf',
        italic: '/fonts/Rokkitt-Italic.ttf'
    },
    'Rubik': {
        regular: '/fonts/Rubik-.ttf', // Keeping as is if no other weight given
        bold: '/fonts/Rubik-Bold.ttf',
        boldItalic: '/fonts/Rubik-BoldItalic.ttf',
        italic: '/fonts/Rubik-Italic.ttf'
    },
    'Rubik Bubbles': {
        regular: '/fonts/RubikBubbles-Regular.ttf'
    },
    'Rubik Dirt': {
        regular: '/fonts/RubikDirt-Regular.ttf'
    },
    'Rubik Glitch': {
        regular: '/fonts/RubikGlitch-Regular.ttf'
    },
    'Rubik Mono One': {
        regular: '/fonts/RubikMonoOne-Regular.ttf'
    },
    'Rum Raisin': {
        regular: '/fonts/RumRaisin-Regular.ttf'
    },
    'Ruthless Sketch': {
        regular: '/fonts/RuthlessSketch.ttf',
        italic: '/fonts/RuthlessSketchItalic.ttf'
    },
    'Salsa': {
        regular: '/fonts/Salsa-Regular.ttf'
    },
    'Satisfy': {
        regular: '/fonts/Satisfy-Regular.ttf'
    },
    'Sedgwick Ave Display': {
        regular: '/fonts/SedgwickAveDisplay-Regular.ttf'
    },
    'Single Day': {
        regular: '/fonts/SingleDay-Regular.ttf'
    },
    'Six Caps': {
        regular: '/fonts/SixCaps-Regular.ttf'
    },
    'Slackey': {
        regular: '/fonts/Slackey-Regular.ttf'
    },
    'Special Elite': {
        regular: '/fonts/SpecialElite-Regular.ttf'
    },
    'Spectral SC': {
        bold: '/fonts/SpectralSC-Bold.ttf',
        boldItalic: '/fonts/SpectralSC-BoldItalic.ttf',
        italic: '/fonts/SpectralSC-Italic.ttf',
        regular: '/fonts/SpectralSC-Regular.ttf'
    },
    'Spectra SC': {
        bold: '/fonts/SpectraSC-Bold.ttf',
        boldItalic: '/fonts/SpectraSC-BoldItalic.ttf',
        italic: '/fonts/SpectraSC-Italic.ttf',
        regular: '/fonts/SpectraSC-Regular.ttf'
    },
    'Spicy Rice': {
        regular: '/fonts/SpicyRice-Regular.ttf'
    },
    'Sue Ellen Francisco': {
        regular: '/fonts/SueEllenFrancisco-Regular.ttf'
    },
    'Syncopate': {
        bold: '/fonts/Syncopate-Bold.ttf',
        regular: '/fonts/Syncopate-Regular.ttf'
    },
    'Tagesschrift': {
        regular: '/fonts/Tagesschrift-Regular.ttf'
    },
    'Thernaly': {
        regular: '/fonts/Thernaly.ttf'
    },
    'Times New Roman': {
        regular: '/fonts/TimesNewRoman.ttf',
        bold: '/fonts/TimesNewRomanBold.ttf',
        boldItalic: '/fonts/TimesNewRomanBoldItalic.ttf',
        italic: '/fonts/TimesNewRomanItalic.ttf'
    },
    'Tiny MCE': {
        regular: '/fonts/tinymce.ttf',
        small: '/fonts/tinymce-small.ttf'
    },
    'Titan One': {
        regular: '/fonts/TitanOne-Regular.ttf'
    },
    'TMT Limkin VF': {
        regular: '/fonts/TMT-LimkinVF.ttf'
    },
    'Trade Winds': {
        regular: '/fonts/TradeWinds-Regular.ttf'
    },
    'Trochut': {
        bold: '/fonts/Trochut-Bold.ttf',
        italic: '/fonts/Trochut-Italic.ttf',
        regular: '/fonts/Trochut-Regular.ttf'
    },
    'TT F': {
        regular: '/fonts/TT_F.ttf'
    },
    'Ubuntu': {
        bold: '/fonts/Ubuntu-Bold.ttf',
        boldItalic: '/fonts/Ubuntu-BoldItalic.ttf',
        italic: '/fonts/Ubuntu-Italic.ttf',
        regular: '/fonts/Ubuntu-Regular.ttf'
    },
    'Ultra': {
        regular: '/fonts/Ultra-Regular.ttf'
    },
    'Unbounded': {
        regular: '/fonts/Unbounded-.ttf', // Keeping as is if no other weight given
        bold: '/fonts/Unbounded-Bold.ttf'
    },
    'Unifraktur Cook': {
        bold: '/fonts/UnifrakturCook-Bold.ttf'
    },
    'Verdana': {
        regular: '/fonts/Verdana.ttf',
        bold: '/fonts/VerdanaBold.ttf',
        boldItalic: '/fonts/VerdanaBoldItalic.ttf',
        italic: '/fonts/VerdanaItalic.ttf'
    },
    'Viga': {
        regular: '/fonts/Viga-Regular.ttf'
    },
    'Vina Sans': {
        regular: '/fonts/VinaSans-Regular.ttf'
    },
    'Voces': {
        regular: '/fonts/Voces-Regular.ttf'
    },
    'Warenhause Standard': {
        regular: '/fonts/Warenhause-Standard.ttf'
    },
    'Water Brush': {
        regular: '/fonts/WaterBrush-Regular.ttf'
    },
    'WDXL Lubrifont TC': {
        regular: '/fonts/WDXLLubrifontTC-Regular.ttf'
    },
    'Willow': {
        regular: '/fonts/willow.ttf'
    },
    'Winky Sans': {
        regular: '/fonts/WinkySans-.ttf', // Keeping as is if no other weight given
        bold: '/fonts/WinkySans-Bold.ttf',
        boldItalic: '/fonts/WinkySans-BoldItalic.ttf',
        italic: '/fonts/WinkySans-Italic.ttf'
    },
    'Work Sans': {
        regular: '/fonts/WorkSans-.ttf', // Keeping as is if no other weight given
        blackItalic: '/fonts/WorkSans-BlackItalic.ttf',
        bold: '/fonts/WorkSans-Bold.ttf',
        boldItalic: '/fonts/WorkSans-BoldItalic.ttf',
    },
    'Xanmono': {
        regular: '/fonts/Xanmono.ttf',
        italic: '/fonts/Xanmonoltallic.ttf' // Assuming 'ltallic' is a typo for 'italic'
    },
    'Zen Dots': {
        regular: '/fonts/ZenDots-Regular.ttf'
    }
};