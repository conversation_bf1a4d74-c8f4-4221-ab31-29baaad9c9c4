<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Artboard Cropping Fix Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563EB;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .result.error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .result.info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
        }
        h1 {
            color: #1F2937;
            text-align: center;
        }
        h3 {
            color: #374151;
            margin-bottom: 10px;
        }
        .instructions {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            color: #92400E;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .fix-highlight {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .before-after {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 20px 0;
        }
        .before, .after {
            padding: 15px;
            border-radius: 6px;
            text-align: center;
        }
        .before {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .after {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
    </style>
</head>
<body>
    <h1>🎯 Artboard Cropping Fix Test</h1>
    
    <div class="fix-highlight">
        <strong>✅ ARTBOARD CROPPING FIX IMPLEMENTED:</strong><br>
        The preview generation now uses proper source coordinates to extract only the artboard area from the main canvas.<br>
        This prevents capturing content outside the artboard boundaries.
    </div>
    
    <div class="instructions">
        <strong>Issue Description:</strong><br>
        The first save preview was showing parts of the canvas outside the artboard boundaries,<br>
        resulting in white/empty areas or content that shouldn't be visible in the preview.
    </div>

    <div class="before-after">
        <div class="before">
            <h4>❌ BEFORE FIX</h4>
            <p>Preview captured entire canvas starting from (0,0)</p>
            <p>Artboard at position (724, 724) was not properly extracted</p>
            <p>Result: White areas or wrong content in preview</p>
        </div>
        <div class="after">
            <h4>✅ AFTER FIX</h4>
            <p>Preview extracts only artboard area using source coordinates</p>
            <p>Uses drawImage(canvas, sx, sy, sw, sh, dx, dy, dw, dh)</p>
            <p>Result: Clean preview showing only artboard content</p>
        </div>
    </div>

    <div class="test-section">
        <h3>Test Artboard Position Detection</h3>
        <p>Check if the artboard coordinates are properly detected.</p>
        <button class="test-button" onclick="testArtboardDetection()">Test Artboard Detection</button>
        <div id="artboardResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Manual Test Steps</h3>
        <p>Follow these steps to test the artboard cropping fix:</p>
        <ol>
            <li><strong>Open Design Editor:</strong> <a href="/design-editor.html" target="_blank">Click here</a></li>
            <li><strong>Add Content:</strong> Add images/text both inside AND outside the artboard</li>
            <li><strong>Apply Effects:</strong> Apply some effects to make content more visible</li>
            <li><strong>Save Project:</strong> Click "Save Project" and check the preview</li>
            <li><strong>Verify:</strong> Preview should show ONLY content within artboard boundaries</li>
        </ol>
        <button class="test-button" onclick="openDesignEditor()">Open Design Editor</button>
    </div>

    <div class="test-section">
        <h3>Technical Fix Details</h3>
        <div class="result info">
🔧 TECHNICAL CHANGES:

OLD CODE (WRONG):
exportCtx.drawImage(mainCanvas, 0, 0);
// This copied entire canvas from (0,0)

NEW CODE (CORRECT):
exportCtx.drawImage(
    mainCanvas,                    // source canvas
    window.artboard.x,             // source x (724)
    window.artboard.y,             // source y (724)  
    window.artboard.width,         // source width (600)
    window.artboard.height,        // source height (600)
    0,                             // destination x (0)
    0,                             // destination y (0)
    window.artboard.width,         // destination width (600)
    window.artboard.height         // destination height (600)
);
// This extracts only the artboard area

RESULT:
- Preview shows only artboard content
- No white areas from outside artboard
- Proper cropping to exact artboard boundaries
        </div>
    </div>

    <div class="test-section">
        <h3>Expected Results</h3>
        <div class="result success">
✅ FIXED BEHAVIOR:
- Preview captures only artboard area (600x600)
- No content from outside artboard boundaries
- Clean, properly cropped preview images
- Effects visible within artboard only

✅ COORDINATES:
- Artboard position: (724, 724) in main canvas
- Artboard size: 600x600 pixels
- Preview extracts exactly this area
- No translation/clipping issues
        </div>
    </div>

    <script>
        function testArtboardDetection() {
            const result = document.getElementById('artboardResult');
            
            try {
                result.textContent = 'Testing artboard detection...\n\n';
                result.className = 'result info';

                let report = 'Artboard Detection Test:\n\n';
                
                // Check if artboard is available
                if (typeof window.artboard !== 'undefined' && window.artboard) {
                    report += '✅ Artboard object found!\n';
                    report += `Position: (${window.artboard.x}, ${window.artboard.y})\n`;
                    report += `Size: ${window.artboard.width} x ${window.artboard.height}\n\n`;
                    
                    // Check if main canvas exists
                    const mainCanvas = document.getElementById('demo');
                    if (mainCanvas) {
                        report += '✅ Main canvas found!\n';
                        report += `Canvas size: ${mainCanvas.width} x ${mainCanvas.height}\n\n`;
                        
                        // Verify artboard is within canvas bounds
                        const artboardRight = window.artboard.x + window.artboard.width;
                        const artboardBottom = window.artboard.y + window.artboard.height;
                        
                        if (artboardRight <= mainCanvas.width && artboardBottom <= mainCanvas.height) {
                            report += '✅ Artboard is within canvas bounds\n';
                            report += 'The fix should work correctly!\n';
                            result.className = 'result success';
                        } else {
                            report += '❌ Artboard extends beyond canvas bounds\n';
                            report += 'This may cause cropping issues\n';
                            result.className = 'result error';
                        }
                    } else {
                        report += '❌ Main canvas not found\n';
                        report += 'Please ensure design editor is loaded\n';
                        result.className = 'result error';
                    }
                } else {
                    report += '❌ Artboard not found\n';
                    report += 'Please ensure design editor is loaded\n';
                    result.className = 'result error';
                }

                result.textContent = report;

            } catch (error) {
                result.textContent = 'Error testing artboard: ' + error.message;
                result.className = 'result error';
            }
        }

        function openDesignEditor() {
            window.open('/design-editor.html', '_blank');
        }
    </script>
</body>
</html>
