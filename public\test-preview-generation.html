<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Preview Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563EB;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .result.error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .result.info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
        }
        .preview-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
        }
        .preview-box {
            border: 2px solid #ddd;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
        }
        .preview-image {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ccc;
        }
        h1 {
            color: #1F2937;
            text-align: center;
        }
        h3 {
            color: #374151;
            margin-bottom: 10px;
        }
        .instructions {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            color: #92400E;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🖼️ Preview Generation Test</h1>
    
    <div class="instructions">
        <strong>Artboard Preview Testing:</strong><br>
        1. Preview should capture ONLY what's inside the artboard (dashed rectangle)<br>
        2. Content outside the artboard should NOT appear in the preview<br>
        3. This matches the expected behavior for design tools<br>
        4. Test by placing content both inside and outside the artboard
    </div>

    <div class="test-section">
        <h3>Test 1: Open Design Editor with Content</h3>
        <p>Open the design editor and add some content to test preview generation.</p>
        <button class="test-button" onclick="openDesignEditor()">Open Design Editor</button>
        <div id="editorResult" class="result info"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Check Image Loading Status</h3>
        <p>Check if all images on the canvas are fully loaded before preview generation.</p>
        <button class="test-button" onclick="checkImageLoading()">Check Image Loading</button>
        <div id="imageResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Test Artboard Preview</h3>
        <p>Test that preview captures only artboard content (as it should).</p>
        <button class="test-button" onclick="testArtboardPreview()">Test Artboard Preview</button>
        <div id="artboardResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Save Project & Check Preview</h3>
        <p>Save a project and verify the preview shows only artboard content.</p>
        <button class="test-button" onclick="testSaveProject()">Test Save Project</button>
        <div id="saveResult" class="result"></div>
    </div>

    <script>
        function openDesignEditor() {
            const result = document.getElementById('editorResult');
            
            const instructions = `
DESIGN EDITOR TESTING:

1. Open: http://localhost:3006/design-editor.html
2. Add some content:
   - Add text: "PREVIEW TEST"
   - Position it anywhere on the canvas
   - Add an image if desired
3. Note the artboard position (gray rectangle)
4. Note where your content is positioned relative to the artboard
5. Come back to this page to test preview generation

Expected Issue:
- If content is outside the artboard area, old method will show empty/small preview
- New method should capture all content regardless of artboard position
            `;
            
            result.textContent = instructions;
            result.className = 'result info';
            
            // Open the design editor in a new tab
            window.open('http://localhost:3006/design-editor.html', '_blank');
        }

        function checkImageLoading() {
            const result = document.getElementById('imageResult');

            const instructions = `
IMAGE LOADING CHECK:

To check if images are fully loaded:
1. Open browser console in the design editor tab
2. Run these commands:

// Check all image objects
const imageObjects = window.canvasObjects ? window.canvasObjects.filter(obj => obj.type === 'image') : [];
console.log('Image objects found:', imageObjects.length);

imageObjects.forEach((obj, index) => {
    console.log('Image', index + 1, ':', {
        id: obj.id,
        imageUrl: obj.imageUrl,
        hasImage: !!obj.image,
        complete: obj.image ? obj.image.complete : false,
        naturalWidth: obj.image ? obj.image.naturalWidth : 0,
        naturalHeight: obj.image ? obj.image.naturalHeight : 0,
        isLoaded: obj.image && obj.image.complete && obj.image.naturalWidth > 0
    });
});

Expected: All images should have isLoaded: true before preview generation!

COMMON ISSUE: If images show isLoaded: false, the preview will be captured
before images are rendered, resulting in missing images in the preview.
                `;

                result.textContent = instructions;
                result.className = 'result info';
        }

        function testArtboardPreview() {
            const result = document.getElementById('artboardResult');

            const instructions = `
ARTBOARD PREVIEW TEST:

The preview should capture ONLY the artboard area:
- Canvas size: artboard.width x artboard.height
- Translation: -artboard.x, -artboard.y
- Clips content to artboard bounds only

To test:
1. Go to design editor console
2. Run this code:

console.log('Artboard bounds:', window.artboard);
console.log('Canvas objects:', window.canvasObjects.map(obj => ({
    type: obj.type,
    x: obj.x,
    y: obj.y,
    text: obj.text || obj.imageUrl
})));

// Check which objects are inside artboard
window.canvasObjects.forEach(obj => {
    const insideX = obj.x >= window.artboard.x && obj.x <= window.artboard.x + window.artboard.width;
    const insideY = obj.y >= window.artboard.y && obj.y <= window.artboard.y + window.artboard.height;
    console.log('Object', obj.type, 'at', obj.x, obj.y, 'inside artboard:', insideX && insideY);
});

Expected: Only objects inside artboard should appear in preview!
            `;

            result.textContent = instructions;
            result.className = 'result info';
        }

        function testSaveProject() {
            const result = document.getElementById('saveResult');

            const instructions = `
SAVE PROJECT TEST:

1. Go to design editor
2. Add content both INSIDE and OUTSIDE the artboard:
   - Place text "INSIDE" within the dashed rectangle
   - Place text "OUTSIDE" beyond the dashed rectangle
3. Click "Save Project" button
4. Go to "My Projects" page
5. Check the preview thumbnail

Expected Result:
- Preview should show only "INSIDE" text
- "OUTSIDE" text should not appear in preview
- Preview should match artboard dimensions
- No small icon should appear

This is the correct behavior for design tools!
            `;

            result.textContent = instructions;
            result.className = 'result info';
        }
    </script>
</body>
</html>
