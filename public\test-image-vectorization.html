<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Vectorization Test - PNG to SVG</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563EB;
        }
        .test-button:disabled {
            background: #9CA3AF;
            cursor: not-allowed;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .result.error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .result.info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
        }
        h1 {
            color: #1F2937;
            text-align: center;
        }
        h3 {
            color: #374151;
            margin-bottom: 10px;
        }
        .instructions {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            color: #92400E;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .image-preview {
            display: flex;
            gap: 20px;
            margin: 20px 0;
            flex-wrap: wrap;
        }
        .preview-container {
            border: 2px dashed #ccc;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            min-width: 300px;
            flex: 1;
        }
        .preview-container img {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .preview-container svg {
            max-width: 100%;
            max-height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .controls {
            margin: 15px 0;
        }
        .controls label {
            display: inline-block;
            margin-right: 10px;
            font-weight: bold;
        }
        .controls input, .controls select {
            margin: 5px;
            padding: 5px;
        }
        .file-input {
            margin: 10px 0;
        }
        .download-link {
            display: inline-block;
            background: #10B981;
            color: white;
            padding: 8px 16px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 5px;
        }
        .download-link:hover {
            background: #059669;
        }
        .algorithm-info {
            background: #F3F4F6;
            border: 1px solid #D1D5DB;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🎨 Image Vectorization Test - PNG to SVG</h1>
    
    <div class="instructions">
        <strong>Purpose:</strong><br>
        This tool converts pixel-based PNG images to vector SVG format for cleaner print-on-demand designs.<br>
        Vector images scale infinitely without quality loss, making them ideal for printing at any size.
    </div>

    <div class="test-section">
        <h3>Load Test Image</h3>
        <p>Load the test image: <code>public/images/sticker (8).png</code></p>
        <button class="test-button" onclick="loadTestImage()">Load Test Image</button>
        <div class="file-input">
            <label for="customImage">Or upload your own image:</label>
            <input type="file" id="customImage" accept="image/*" onchange="loadCustomImage(event)">
        </div>
        <div id="loadResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Vectorization Controls</h3>
        <div class="controls">
            <label for="algorithm">Algorithm:</label>
            <select id="algorithm">
                <option value="simple">Simple Threshold (Recommended for testing)</option>
                <option value="potrace">Potrace (Edge Tracing)</option>
                <option value="autotrace">AutoTrace (Color Reduction)</option>
            </select>

            <label for="threshold">Threshold (0=black only, 255=all pixels):</label>
            <input type="range" id="threshold" min="0" max="255" value="128">
            <span id="thresholdValue">128</span>
            <button class="test-button" onclick="suggestThreshold()" style="font-size: 12px; padding: 4px 8px;">Auto-suggest</button>

            <label for="smoothing">Smoothing/Detail Level:</label>
            <input type="range" id="smoothing" min="1" max="20" value="4">
            <span id="smoothingValue">4</span>
        </div>

        <div class="controls">
            <button class="test-button" onclick="previewThreshold()" style="background: #059669;">Preview Threshold</button>
            <button class="test-button" onclick="quickTest()" style="background: #7C3AED;">Quick Test (Small Area)</button>
        </div>
        
        <button class="test-button" onclick="vectorizeImage()" id="vectorizeBtn" disabled>Vectorize Image</button>
        <button class="test-button" onclick="resetSettings()">Reset Settings</button>
        
        <div class="algorithm-info">
            <strong>Algorithm Info:</strong>
            <div id="algorithmInfo">Select an algorithm to see details</div>
        </div>
    </div>

    <div class="test-section">
        <h3>Image Preview</h3>
        <div class="image-preview">
            <div class="preview-container">
                <h4>Original PNG</h4>
                <div id="originalPreview">No image loaded</div>
            </div>
            <div class="preview-container">
                <h4>Vectorized SVG</h4>
                <div id="vectorizedPreview">No vectorization yet</div>
            </div>
        </div>
    </div>

    <div class="test-section">
        <h3>Download Results</h3>
        <div id="downloadLinks"></div>
        <div id="vectorizationResult" class="result"></div>
    </div>

    <script>
        let currentImage = null;
        let vectorizedSVG = null;

        // Update threshold display
        document.getElementById('threshold').addEventListener('input', function() {
            document.getElementById('thresholdValue').textContent = this.value;
        });

        // Update smoothing display
        document.getElementById('smoothing').addEventListener('input', function() {
            document.getElementById('smoothingValue').textContent = this.value;
        });

        // Update algorithm info
        document.getElementById('algorithm').addEventListener('change', function() {
            const info = {
                'potrace': 'Potrace: Traces bitmap edges to create smooth vector paths. Best for logos and simple graphics.',
                'autotrace': 'AutoTrace: Reduces colors and creates vector shapes. Good for complex images with multiple colors.',
                'simple': 'Simple Threshold: Basic black/white conversion. Fast but less sophisticated.'
            };
            document.getElementById('algorithmInfo').textContent = info[this.value];
        });

        function loadTestImage() {
            const result = document.getElementById('loadResult');
            result.textContent = 'Loading test image...';
            result.className = 'result info';

            const img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = function() {
                currentImage = img;
                displayOriginalImage(img);
                document.getElementById('vectorizeBtn').disabled = false;
                result.textContent = 'Test image loaded successfully!\nImage size: ' + img.width + 'x' + img.height;
                result.className = 'result success';
            };
            img.onerror = function() {
                result.textContent = 'Error loading test image. Please check the file path.\nTried to load: images/sticker (8).png';
                result.className = 'result error';
            };
            img.src = 'images/sticker (8).png';
        }

        function loadCustomImage(event) {
            const file = event.target.files[0];
            if (!file) return;

            const result = document.getElementById('loadResult');
            result.textContent = 'Loading custom image...';
            result.className = 'result info';

            const reader = new FileReader();
            reader.onload = function(e) {
                const img = new Image();
                img.onload = function() {
                    currentImage = img;
                    displayOriginalImage(img);
                    document.getElementById('vectorizeBtn').disabled = false;
                    result.textContent = 'Custom image loaded successfully!';
                    result.className = 'result success';
                };
                img.src = e.target.result;
            };
            reader.readAsDataURL(file);
        }

        function displayOriginalImage(img) {
            const preview = document.getElementById('originalPreview');
            preview.innerHTML = '';
            const displayImg = img.cloneNode();
            displayImg.style.maxWidth = '100%';
            displayImg.style.maxHeight = '200px';
            preview.appendChild(displayImg);
        }

        function vectorizeImage() {
            if (!currentImage) {
                alert('Please load an image first');
                return;
            }

            const result = document.getElementById('vectorizationResult');
            result.textContent = 'Starting vectorization process...';
            result.className = 'result info';

            const algorithm = document.getElementById('algorithm').value;
            const threshold = parseInt(document.getElementById('threshold').value);
            const smoothing = parseInt(document.getElementById('smoothing').value);

            console.log('🎨 Starting vectorization with:', {algorithm, threshold, smoothing});
            result.textContent += '\n📊 Algorithm: ' + algorithm + ', Threshold: ' + threshold + ', Smoothing: ' + smoothing;

            try {
                // Create canvas for image processing
                result.textContent += '\n🖼️ Creating canvas...';
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = currentImage.width;
                canvas.height = currentImage.height;

                console.log('🖼️ Canvas created:', canvas.width, 'x', canvas.height);
                result.textContent += '\n✅ Canvas created: ' + canvas.width + 'x' + canvas.height;

                // Draw image to canvas
                result.textContent += '\n🎨 Drawing image to canvas...';
                ctx.drawImage(currentImage, 0, 0);

                // Get image data
                result.textContent += '\n📊 Extracting pixel data...';
                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                console.log('📊 Image data extracted, length:', imageData.data.length);
                result.textContent += '\n✅ Extracted ' + (imageData.data.length / 4) + ' pixels';

                // Analyze image before processing
                updateResult(result, '\n🔍 Analyzing image colors...');
                const analysis = analyzeImageData(imageData);
                console.log('🔍 Image analysis:', analysis);
                updateResult(result, '\n📈 Analysis: Avg brightness: ' + analysis.avgBrightness.toFixed(1) +
                                    ', Dark pixels: ' + analysis.darkPixels + '/' + analysis.totalPixels +
                                    ' (' + (analysis.darkPixels/analysis.totalPixels*100).toFixed(1) + '%)');

                // Suggest better threshold if needed
                if (analysis.darkPixels < analysis.totalPixels * 0.01) {
                    updateResult(result, '\n⚠️ Very few dark pixels detected. Try lowering threshold to ' + Math.floor(analysis.avgBrightness * 0.8));
                } else if (analysis.darkPixels > analysis.totalPixels * 0.9) {
                    updateResult(result, '\n⚠️ Too many dark pixels detected. Try raising threshold to ' + Math.floor(analysis.avgBrightness * 1.2));
                }

                // Apply vectorization algorithm
                let svg;
                result.textContent += '\n⚙️ Processing with ' + algorithm + ' algorithm...';

                switch(algorithm) {
                    case 'potrace':
                        svg = potraceVectorize(imageData, threshold, smoothing, result);
                        break;
                    case 'autotrace':
                        svg = autotraceVectorize(imageData, threshold, smoothing, result);
                        break;
                    case 'simple':
                        svg = simpleVectorize(imageData, threshold, smoothing, result);
                        break;
                    default:
                        throw new Error('Unknown algorithm: ' + algorithm);
                }

                console.log('✅ SVG generated, length:', svg.length);
                result.textContent += '\n✅ SVG generated (' + svg.length + ' characters)';

                vectorizedSVG = svg;
                displayVectorizedImage(svg);
                createDownloadLinks(svg);

                result.textContent += '\n🎉 Vectorization completed successfully!';
                result.className = 'result success';

            } catch (error) {
                console.error('❌ Vectorization error:', error);
                result.textContent += '\n❌ Error: ' + error.message;
                result.className = 'result error';
            }
        }

        function analyzeImageData(imageData) {
            const data = imageData.data;
            let totalBrightness = 0;
            let darkPixels = 0;
            let transparentPixels = 0;
            const totalPixels = imageData.width * imageData.height;

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const alpha = data[i + 3];

                if (alpha < 128) {
                    transparentPixels++;
                    continue;
                }

                const brightness = (r + g + b) / 3;
                totalBrightness += brightness;

                if (brightness < 128) {
                    darkPixels++;
                }
            }

            return {
                totalPixels,
                darkPixels,
                transparentPixels,
                avgBrightness: totalBrightness / (totalPixels - transparentPixels),
                hasTransparency: transparentPixels > 0
            };
        }

        function simpleVectorize(imageData, threshold, smoothing, result) {
            console.log('🔧 Simple vectorize started');
            const width = imageData.width;
            const height = imageData.height;
            const data = imageData.data;

            console.log('🔧 Processing image:', width, 'x', height, 'threshold:', threshold);
            result.textContent += '\n🔧 Simple algorithm processing ' + width + 'x' + height + ' image...';

            // Convert to binary based on threshold
            const binaryData = [];
            let blackPixels = 0;
            let transparentPixels = 0;
            let colorSamples = [];

            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const alpha = data[i + 3];

                // Sample some colors for debugging
                if (colorSamples.length < 10 && i % 1000 === 0) {
                    colorSamples.push({r, g, b, alpha, brightness: (r + g + b) / 3});
                }

                if (alpha < 128) {
                    transparentPixels++;
                    binaryData.push(0); // Treat transparent as white
                    continue;
                }

                const gray = (r + g + b) / 3;
                const isBlack = gray <= threshold ? 1 : 0;
                binaryData.push(isBlack);
                if (isBlack) blackPixels++;
            }

            console.log('🔧 Color samples:', colorSamples);
            console.log('🔧 Black pixels found:', blackPixels, 'Transparent:', transparentPixels);
            result.textContent += '\n📊 Found ' + blackPixels + ' dark pixels, ' + transparentPixels + ' transparent pixels';

            if (blackPixels === 0) {
                result.textContent += '\n⚠️ No dark pixels found! Try lowering the threshold.';
                return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <text x="10" y="30" fill="red" font-size="16">No dark pixels found - try lower threshold</text>
                </svg>`;
            }

            // Create smooth vector paths instead of rectangles
            updateResult(result, '\n🎨 Creating smooth vector paths (smoothing: ' + smoothing + ')...');
            return createSmoothVectorPathsWithSmoothing(binaryData, width, height, blackPixels, smoothing, result);
        }

        function createSmoothVectorPaths(binaryData, width, height, blackPixels, result) {
            // Downsample the image for smoother results
            const scale = Math.max(1, Math.floor(Math.min(width, height) / 200)); // Target ~200px max dimension
            const newWidth = Math.floor(width / scale);
            const newHeight = Math.floor(height / scale);

            updateResult(result, '\n📐 Downsampling from ' + width + 'x' + height + ' to ' + newWidth + 'x' + newHeight + ' (scale: ' + scale + ')');

            // Create downsampled binary data
            const downsampledData = [];
            for (let y = 0; y < newHeight; y++) {
                for (let x = 0; x < newWidth; x++) {
                    // Sample a block and take majority vote
                    let blackCount = 0;
                    let totalCount = 0;

                    for (let dy = 0; dy < scale && y * scale + dy < height; dy++) {
                        for (let dx = 0; dx < scale && x * scale + dx < width; dx++) {
                            const idx = (y * scale + dy) * width + (x * scale + dx);
                            if (idx < binaryData.length) {
                                if (binaryData[idx] === 1) blackCount++;
                                totalCount++;
                            }
                        }
                    }

                    // Majority vote
                    downsampledData.push(blackCount > totalCount / 2 ? 1 : 0);
                }
            }

            updateResult(result, '\n🔍 Finding contours...');

            // Find contours using marching squares algorithm
            const contours = findContours(downsampledData, newWidth, newHeight);

            updateResult(result, '\n✅ Found ' + contours.length + ' contours');

            // Convert contours to smooth SVG paths
            let paths = '';
            contours.forEach((contour, index) => {
                if (contour.length > 3) {
                    const smoothedContour = smoothContour(contour, 2); // Smooth the contour
                    const pathData = contourToSVGPath(smoothedContour, scale); // Scale back up
                    paths += `<path d="${pathData}" fill="black" stroke="none"/>`;
                }
            });

            updateResult(result, '\n🎨 Created ' + contours.length + ' smooth vector paths');

            return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">
                ${paths}
            </svg>`;
        }

        function findContours(binaryData, width, height) {
            const contours = [];

            // Validate inputs to prevent array length errors
            if (!binaryData || width <= 0 || height <= 0 || binaryData.length !== width * height) {
                console.error('Invalid findContours parameters:', {
                    binaryDataLength: binaryData ? binaryData.length : 'null',
                    width, height,
                    expectedLength: width * height
                });
                return contours;
            }

            const visited = new Array(width * height).fill(false);

            for (let y = 0; y < height - 1; y++) {
                for (let x = 0; x < width - 1; x++) {
                    const idx = y * width + x;
                    if (idx < binaryData.length && binaryData[idx] === 1 && !visited[idx]) {
                        const contour = traceContour(binaryData, width, height, x, y, visited);
                        if (contour.length > 8) { // Only keep significant contours
                            contours.push(contour);
                        }
                    }
                }
            }

            return contours;
        }

        function traceContour(binaryData, width, height, startX, startY, visited) {
            const contour = [];
            const directions = [
                {x: 1, y: 0},   // right
                {x: 1, y: 1},   // down-right
                {x: 0, y: 1},   // down
                {x: -1, y: 1},  // down-left
                {x: -1, y: 0},  // left
                {x: -1, y: -1}, // up-left
                {x: 0, y: -1},  // up
                {x: 1, y: -1}   // up-right
            ];

            let x = startX, y = startY;
            let dir = 0;
            const maxSteps = 1000;
            let steps = 0;

            do {
                const idx = y * width + x;
                if (idx >= 0 && idx < binaryData.length) {
                    visited[idx] = true;
                    contour.push({x, y});
                }

                // Find next point on contour
                let found = false;
                for (let i = 0; i < 8; i++) {
                    const newDir = (dir + i) % 8;
                    const newX = x + directions[newDir].x;
                    const newY = y + directions[newDir].y;
                    const newIdx = newY * width + newX;

                    if (newX >= 0 && newX < width && newY >= 0 && newY < height &&
                        newIdx < binaryData.length && binaryData[newIdx] === 1) {
                        x = newX;
                        y = newY;
                        dir = newDir;
                        found = true;
                        break;
                    }
                }

                if (!found) break;
                steps++;

            } while (steps < maxSteps && (x !== startX || y !== startY || contour.length < 4));

            return contour;
        }

        function smoothContour(contour, smoothness) {
            if (!contour || contour.length < 3) return contour || [];

            const smoothed = [];
            const factor = Math.max(1, Math.floor(smoothness));

            for (let i = 0; i < contour.length; i += factor) {
                // Average nearby points for smoothing
                let avgX = 0, avgY = 0, count = 0;

                for (let j = -factor; j <= factor; j++) {
                    const idx = (i + j + contour.length) % contour.length;
                    const point = contour[idx];

                    // Validate point exists and has x,y properties
                    if (point && typeof point.x === 'number' && typeof point.y === 'number') {
                        avgX += point.x;
                        avgY += point.y;
                        count++;
                    }
                }

                if (count > 0) {
                    smoothed.push({
                        x: avgX / count,
                        y: avgY / count
                    });
                }
            }

            return smoothed.length > 0 ? smoothed : contour;
        }

        function contourToSVGPath(contour, scale) {
            if (contour.length < 2) return '';

            let path = `M${contour[0].x * scale},${contour[0].y * scale}`;

            // Use quadratic curves for smoother paths
            for (let i = 1; i < contour.length; i++) {
                const curr = contour[i];
                const next = contour[(i + 1) % contour.length];

                // Control point is halfway between current and next
                const cpX = (curr.x + next.x) / 2 * scale;
                const cpY = (curr.y + next.y) / 2 * scale;

                path += ` Q${curr.x * scale},${curr.y * scale} ${cpX},${cpY}`;
            }

            path += ' Z'; // Close the path
            return path;
        }

        function createSmoothVectorPathsForColor(binaryData, width, height, pixelCount, r, g, b, smoothing, result) {
            // Validate inputs
            if (!binaryData || width <= 0 || height <= 0 || binaryData.length !== width * height) {
                console.error('Invalid color vectorization inputs:', {
                    binaryDataLength: binaryData ? binaryData.length : 'null',
                    width, height, expectedLength: width * height
                });
                return '';
            }

            // Higher resolution downsampling - target larger images for better quality
            const targetSize = smoothing > 10 ? 300 : 500; // Higher smoothing = smaller target for performance
            const baseScale = Math.max(1, Math.floor(Math.min(width, height) / targetSize));
            const smoothScale = Math.max(1, Math.floor(baseScale * Math.max(1, smoothing / 8))); // Less aggressive smoothing scaling
            const newWidth = Math.max(1, Math.floor(width / smoothScale));
            const newHeight = Math.max(1, Math.floor(height / smoothScale));

            // Create downsampled binary data
            const downsampledData = [];
            for (let y = 0; y < newHeight; y++) {
                for (let x = 0; x < newWidth; x++) {
                    let blackCount = 0;
                    let totalCount = 0;

                    for (let dy = 0; dy < smoothScale && y * smoothScale + dy < height; dy++) {
                        for (let dx = 0; dx < smoothScale && x * smoothScale + dx < width; dx++) {
                            const idx = (y * smoothScale + dy) * width + (x * smoothScale + dx);
                            if (idx >= 0 && idx < binaryData.length) {
                                if (binaryData[idx] === 1) blackCount++;
                                totalCount++;
                            }
                        }
                    }

                    downsampledData.push(blackCount > totalCount / 2 ? 1 : 0);
                }
            }

            // Find contours
            const contours = findContours(downsampledData, newWidth, newHeight);

            // Convert contours to smooth SVG paths with color
            let paths = '';
            contours.forEach((contour, index) => {
                if (contour.length > Math.max(3, 20 - smoothing)) { // Smoothing affects minimum contour size
                    const smoothedContour = smoothContour(contour, Math.max(1, smoothing / 3));
                    const pathData = contourToSVGPath(smoothedContour, smoothScale);
                    paths += `<path d="${pathData}" fill="rgb(${r},${g},${b})" stroke="none"/>`;
                }
            });

            return paths;
        }

        function createSmoothVectorPathsWithSmoothing(binaryData, width, height, blackPixels, smoothing, result) {
            // Validate inputs
            if (!binaryData || width <= 0 || height <= 0 || binaryData.length !== width * height) {
                updateResult(result, '\n❌ Invalid input data for vectorization');
                return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <text x="10" y="30" fill="red" font-size="16">Invalid input data</text>
                </svg>`;
            }

            // Higher resolution processing - less aggressive downsampling
            const targetSize = smoothing > 10 ? 400 : 600; // Higher smoothing = smaller target for performance
            const baseScale = Math.max(1, Math.floor(Math.min(width, height) / targetSize));
            const smoothScale = Math.max(1, Math.floor(baseScale * Math.max(1, smoothing / 8))); // Less aggressive smoothing scaling
            const newWidth = Math.max(1, Math.floor(width / smoothScale));
            const newHeight = Math.max(1, Math.floor(height / smoothScale));

            updateResult(result, '\n📐 Downsampling from ' + width + 'x' + height + ' to ' + newWidth + 'x' + newHeight + ' (smooth scale: ' + smoothScale + ')');

            // Create downsampled binary data
            const downsampledData = [];
            for (let y = 0; y < newHeight; y++) {
                for (let x = 0; x < newWidth; x++) {
                    let blackCount = 0;
                    let totalCount = 0;

                    for (let dy = 0; dy < smoothScale && y * smoothScale + dy < height; dy++) {
                        for (let dx = 0; dx < smoothScale && x * smoothScale + dx < width; dx++) {
                            const idx = (y * smoothScale + dy) * width + (x * smoothScale + dx);
                            if (idx >= 0 && idx < binaryData.length) {
                                if (binaryData[idx] === 1) blackCount++;
                                totalCount++;
                            }
                        }
                    }

                    downsampledData.push(blackCount > totalCount / 2 ? 1 : 0);
                }
            }

            updateResult(result, '\n🔍 Finding contours...');

            // Find contours
            const contours = findContours(downsampledData, newWidth, newHeight);

            updateResult(result, '\n✅ Found ' + contours.length + ' contours');

            // Convert contours to smooth SVG paths
            let paths = '';
            const minContourSize = Math.max(3, 20 - smoothing); // Higher smoothing = smaller minimum contour size

            contours.forEach((contour, index) => {
                if (contour.length > minContourSize) {
                    const smoothedContour = smoothContour(contour, Math.max(1, smoothing / 3)); // Smoothing affects contour smoothing
                    const pathData = contourToSVGPath(smoothedContour, smoothScale);
                    paths += `<path d="${pathData}" fill="black" stroke="none"/>`;
                }
            });

            updateResult(result, '\n🎨 Created ' + contours.filter(c => c.length > minContourSize).length + ' smooth vector paths');

            return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">
                ${paths}
            </svg>`;
        }

        function potraceVectorize(imageData, threshold, smoothing, result) {
            console.log('🎯 Potrace vectorize started');
            const width = imageData.width;
            const height = imageData.height;
            const data = imageData.data;

            updateResult(result, '\n🎯 Potrace: Converting to binary...');

            // Convert to binary
            const binary = [];
            let darkPixels = 0;
            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const alpha = data[i + 3];
                const gray = alpha < 128 ? 255 : (r + g + b) / 3;
                const isDark = gray <= threshold ? 1 : 0;
                binary.push(isDark);
                if (isDark) darkPixels++;
            }

            console.log('🎯 Dark pixels for tracing:', darkPixels);
            updateResult(result, '\n🎯 Found ' + darkPixels + ' pixels to trace');

            if (darkPixels === 0) {
                return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <text x="10" y="30" fill="red" font-size="16">No pixels to trace - try lower threshold</text>
                </svg>`;
            }

            // Use smooth vectorization approach with smoothing
            updateResult(result, '\n🎯 Creating smooth vector paths (smoothing: ' + smoothing + ')...');
            return createSmoothVectorPathsWithSmoothing(binary, width, height, darkPixels, smoothing, result);
        }

        function createSmoothPath(component, bounds, smoothing) {
            // Simple rectangular path for now - can be enhanced with actual curve fitting
            const margin = Math.max(0, smoothing - 2);
            return `M${bounds.minX - margin},${bounds.minY - margin} L${bounds.maxX + margin},${bounds.minY - margin} L${bounds.maxX + margin},${bounds.maxY + margin} L${bounds.minX - margin},${bounds.maxY + margin} Z`;
        }

        function autotraceVectorize(imageData, threshold, smoothing, result) {
            console.log('🌈 AutoTrace vectorize started');
            const width = imageData.width;
            const height = imageData.height;
            const data = imageData.data;

            updateResult(result, '\n🌈 AutoTrace: Analyzing colors with threshold ' + threshold + '...');

            // Extract colors that are darker than threshold
            const colorMap = new Map();
            for (let i = 0; i < data.length; i += 4) {
                const r = data[i];
                const g = data[i + 1];
                const b = data[i + 2];
                const alpha = data[i + 3];

                if (alpha > 128) {
                    const brightness = (r + g + b) / 3;
                    // Only include colors darker than threshold
                    if (brightness <= threshold) {
                        // Quantize colors to reduce palette
                        const qr = Math.floor(r / 32) * 32;
                        const qg = Math.floor(g / 32) * 32;
                        const qb = Math.floor(b / 32) * 32;
                        const colorKey = `${qr},${qg},${qb}`;
                        colorMap.set(colorKey, (colorMap.get(colorKey) || 0) + 1);
                    }
                }
            }

            // Get top colors
            const topColors = Array.from(colorMap.entries())
                .sort((a, b) => b[1] - a[1])
                .slice(0, 8)
                .map(([color, count]) => {
                    const [r, g, b] = color.split(',').map(Number);
                    return {r, g, b, count};
                });

            console.log('🌈 Top colors found:', topColors);
            result.textContent += '\n🌈 Found ' + topColors.length + ' dominant colors';

            let paths = '';
            let totalShapes = 0;

            topColors.forEach(({r, g, b, count}, index) => {
                // Check if we're approaching string length limits
                if (paths.length > 50000000) { // 50MB limit
                    updateResult(result, '\n⚠️ Reached size limit, stopping processing');
                    return;
                }

                updateResult(result, '\n🎨 Processing color ' + (index + 1) + ': rgb(' + r + ',' + g + ',' + b + ') - ' + count + ' pixels');

                // Create binary mask for this color
                const mask = [];
                let colorPixels = 0;

                for (let i = 0; i < data.length; i += 4) {
                    const dr = Math.abs(data[i] - r);
                    const dg = Math.abs(data[i + 1] - g);
                    const db = Math.abs(data[i + 2] - b);
                    const alpha = data[i + 3];

                    const isMatch = alpha > 128 && dr < 64 && dg < 64 && db < 64;
                    mask.push(isMatch ? 1 : 0);
                    if (isMatch) colorPixels++;
                }

                console.log(`🎨 Color rgb(${r},${g},${b}): ${colorPixels} matching pixels found`);

                if (colorPixels < 100) {
                    updateResult(result, ' - Skipped (too few pixels)');
                    return;
                }

                // Use smooth vectorization for this color
                updateResult(result, ' - Creating smooth paths...');
                const colorSVG = createSmoothVectorPathsForColor(mask, width, height, colorPixels, r, g, b, smoothing, result);

                // Check if adding this would exceed limits
                if (paths.length + colorSVG.length > 100000000) { // 100MB total limit
                    updateResult(result, ' - Skipped (would exceed size limit)');
                    return;
                }

                paths += colorSVG;

                // Count paths in the SVG
                const pathCount = (colorSVG.match(/<path/g) || []).length;
                totalShapes += pathCount;

                console.log(`🎨 Color rgb(${r},${g},${b}): ${pathCount} smooth paths created`);
                updateResult(result, ' - ' + pathCount + ' smooth paths');
            });

            console.log('🌈 Total shapes created:', totalShapes);
            result.textContent += '\n✅ Created ' + totalShapes + ' colored shapes';

            if (totalShapes === 0) {
                return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <text x="10" y="30" fill="red" font-size="16">No shapes created - try adjusting threshold</text>
                </svg>`;
            }

            // Create SVG with error handling for large strings
            try {
                const svg = `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 ${width} ${height}">
                    ${paths}
                </svg>`;

                if (svg.length > 200000000) { // 200MB limit
                    result.textContent += '\n⚠️ Result too large, creating simplified version';
                    return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                        <text x="10" y="30" fill="red" font-size="16">Result too large - try higher smoothing value</text>
                    </svg>`;
                }

                return svg;
            } catch (error) {
                result.textContent += '\n❌ SVG creation failed: ' + error.message;
                return `<svg width="${width}" height="${height}" xmlns="http://www.w3.org/2000/svg">
                    <text x="10" y="30" fill="red" font-size="16">SVG too large - try higher smoothing</text>
                </svg>`;
            }
        }

        function updateResult(resultElement, newText) {
            resultElement.textContent += newText;
            // Force a repaint to show the update immediately
            resultElement.scrollTop = resultElement.scrollHeight;
        }

        function floodFill(binary, width, height, startX, startY, visited) {
            const stack = [{x: startX, y: startY}];
            const component = [];
            const maxComponent = 10000; // Prevent infinite loops

            while (stack.length > 0 && component.length < maxComponent) {
                const {x, y} = stack.pop();
                const idx = y * width + x;

                // Check bounds and conditions
                if (x < 0 || x >= width || y < 0 || y >= height ||
                    idx >= binary.length || visited[idx] || binary[idx] !== 1) {
                    continue;
                }

                visited[idx] = true;
                component.push({x, y});

                // Add neighbors (4-connected)
                stack.push(
                    {x: x + 1, y: y},
                    {x: x - 1, y: y},
                    {x: x, y: y + 1},
                    {x: x, y: y - 1}
                );
            }

            return component;
        }

        function getBounds(points) {
            if (points.length === 0) return {minX: 0, maxX: 0, minY: 0, maxY: 0, width: 0, height: 0};

            const minX = Math.min(...points.map(p => p.x));
            const maxX = Math.max(...points.map(p => p.x));
            const minY = Math.min(...points.map(p => p.y));
            const maxY = Math.max(...points.map(p => p.y));

            return {
                minX, maxX, minY, maxY,
                width: maxX - minX,
                height: maxY - minY
            };
        }

        function displayVectorizedImage(svg) {
            const preview = document.getElementById('vectorizedPreview');
            preview.innerHTML = svg;
        }

        function createDownloadLinks(svg) {
            const linksContainer = document.getElementById('downloadLinks');

            // Create SVG download link
            const svgBlob = new Blob([svg], {type: 'image/svg+xml'});
            const svgUrl = URL.createObjectURL(svgBlob);

            linksContainer.innerHTML = `
                <a href="${svgUrl}" download="vectorized-image.svg" class="download-link">
                    Download SVG
                </a>
                <button class="test-button" onclick="copyToClipboard()">Copy SVG Code</button>
                <button class="test-button" onclick="showSVGCode()">View SVG Code</button>
            `;
        }

        function copyToClipboard() {
            if (!vectorizedSVG) return;

            navigator.clipboard.writeText(vectorizedSVG).then(() => {
                const result = document.getElementById('vectorizationResult');
                result.textContent = 'SVG code copied to clipboard!';
                result.className = 'result success';
            }).catch(err => {
                const result = document.getElementById('vectorizationResult');
                result.textContent = 'Failed to copy to clipboard: ' + err.message;
                result.className = 'result error';
            });
        }

        function showSVGCode() {
            if (!vectorizedSVG) return;

            const result = document.getElementById('vectorizationResult');
            result.textContent = vectorizedSVG;
            result.className = 'result info';
        }

        function resetSettings() {
            document.getElementById('algorithm').value = 'potrace';
            document.getElementById('threshold').value = 128;
            document.getElementById('smoothing').value = 2;
            document.getElementById('thresholdValue').textContent = '128';
            document.getElementById('smoothingValue').textContent = '2';
            document.getElementById('algorithmInfo').textContent = 'Potrace: Traces bitmap edges to create smooth vector paths. Best for logos and simple graphics.';

            const result = document.getElementById('vectorizationResult');
            result.textContent = 'Settings reset to defaults.';
            result.className = 'result info';
        }

        function suggestThreshold() {
            if (!currentImage) {
                alert('Please load an image first');
                return;
            }

            const result = document.getElementById('vectorizationResult');
            result.textContent = 'Analyzing image to suggest optimal threshold...';
            result.className = 'result info';

            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = currentImage.width;
                canvas.height = currentImage.height;
                ctx.drawImage(currentImage, 0, 0);

                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const analysis = analyzeImageData(imageData);

                // Suggest threshold based on average brightness
                let suggestedThreshold;
                if (analysis.avgBrightness > 200) {
                    suggestedThreshold = Math.floor(analysis.avgBrightness * 0.7); // Bright image
                } else if (analysis.avgBrightness < 100) {
                    suggestedThreshold = Math.floor(analysis.avgBrightness * 1.5); // Dark image
                } else {
                    suggestedThreshold = Math.floor(analysis.avgBrightness * 0.8); // Normal image
                }

                suggestedThreshold = Math.max(10, Math.min(245, suggestedThreshold));

                document.getElementById('threshold').value = suggestedThreshold;
                document.getElementById('thresholdValue').textContent = suggestedThreshold;

                result.textContent = `Suggested threshold: ${suggestedThreshold}\nBased on average brightness: ${analysis.avgBrightness.toFixed(1)}\nDark pixels at this threshold: ${Math.round((analysis.darkPixels / analysis.totalPixels) * 100)}%`;
                result.className = 'result success';

            } catch (error) {
                result.textContent = 'Error analyzing image: ' + error.message;
                result.className = 'result error';
            }
        }

        function previewThreshold() {
            if (!currentImage) {
                alert('Please load an image first');
                return;
            }

            const threshold = parseInt(document.getElementById('threshold').value);
            const result = document.getElementById('vectorizationResult');
            result.textContent = 'Creating threshold preview...';
            result.className = 'result info';

            try {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                canvas.width = currentImage.width;
                canvas.height = currentImage.height;
                ctx.drawImage(currentImage, 0, 0);

                const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
                const data = imageData.data;

                // Create binary preview
                for (let i = 0; i < data.length; i += 4) {
                    const r = data[i];
                    const g = data[i + 1];
                    const b = data[i + 2];
                    const alpha = data[i + 3];

                    if (alpha < 128) {
                        data[i] = data[i + 1] = data[i + 2] = 255; // White for transparent
                    } else {
                        const gray = (r + g + b) / 3;
                        const value = gray <= threshold ? 0 : 255;
                        data[i] = data[i + 1] = data[i + 2] = value;
                    }
                }

                ctx.putImageData(imageData, 0, 0);

                // Display preview
                const preview = document.getElementById('vectorizedPreview');
                preview.innerHTML = '<h4>Threshold Preview</h4>';
                const previewCanvas = canvas.cloneNode();
                previewCanvas.getContext('2d').putImageData(imageData, 0, 0);
                previewCanvas.style.maxWidth = '100%';
                previewCanvas.style.maxHeight = '200px';
                previewCanvas.style.border = '1px solid #ddd';
                preview.appendChild(previewCanvas);

                result.textContent = `Threshold preview created.\nBlack pixels will become vector shapes.\nWhite pixels will be background.`;
                result.className = 'result success';

            } catch (error) {
                result.textContent = 'Error creating preview: ' + error.message;
                result.className = 'result error';
            }
        }

        function quickTest() {
            if (!currentImage) {
                alert('Please load an image first');
                return;
            }

            const result = document.getElementById('vectorizationResult');
            result.textContent = 'Running quick test on small area...';
            result.className = 'result info';

            try {
                // Create small test area (100x100 from center)
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const testSize = 100;
                canvas.width = testSize;
                canvas.height = testSize;

                const startX = Math.max(0, (currentImage.width - testSize) / 2);
                const startY = Math.max(0, (currentImage.height - testSize) / 2);

                ctx.drawImage(currentImage, startX, startY, testSize, testSize, 0, 0, testSize, testSize);

                const imageData = ctx.getImageData(0, 0, testSize, testSize);
                const threshold = parseInt(document.getElementById('threshold').value);

                const svg = simpleVectorize(imageData, threshold, result);

                // Display quick test result
                const preview = document.getElementById('vectorizedPreview');
                preview.innerHTML = '<h4>Quick Test Result (100x100 area)</h4>' + svg;

                result.textContent += '\n🚀 Quick test completed! This shows how the full image will look.';
                result.className = 'result success';

            } catch (error) {
                result.textContent = 'Error in quick test: ' + error.message;
                result.className = 'result error';
            }
        }

        // Initialize algorithm info on page load
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('algorithmInfo').textContent = 'Simple Threshold: Basic black/white conversion. Fast but less sophisticated. Good for testing.';
        });
    </script>
</body>
</html>
