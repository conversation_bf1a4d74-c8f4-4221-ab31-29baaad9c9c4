<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Preview Tool</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .header {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }

        .header h1 {
            margin: 0 0 20px 0;
            color: #333;
        }

        .input-group {
            display: flex;
            gap: 10px;
            align-items: center;
            flex-wrap: wrap;
        }

        .input-group label {
            font-weight: bold;
            color: #555;
        }

        .input-group input {
            padding: 10px;
            border: 2px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
            min-width: 200px;
        }

        .input-group button {
            padding: 10px 20px;
            background: #007cba;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            font-weight: bold;
        }

        .input-group button:hover {
            background: #005a87;
        }

        .font-size-controls {
            margin-top: 10px;
            display: flex;
            gap: 10px;
            align-items: center;
        }

        .font-size-controls input[type="range"] {
            width: 200px;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
            font-style: italic;
        }

        .font-table {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .font-table table {
            width: 100%;
            border-collapse: collapse;
        }

        .font-table th {
            background: #f8f9fa;
            padding: 15px;
            text-align: left;
            font-weight: bold;
            color: #333;
            border-bottom: 2px solid #dee2e6;
        }

        .font-table td {
            padding: 15px;
            border-bottom: 1px solid #dee2e6;
            vertical-align: middle;
        }

        .font-table tr:hover {
            background-color: #f8f9fa;
        }

        .font-name {
            font-weight: bold;
            color: #495057;
            min-width: 200px;
        }

        .font-preview {
            font-size: 24px;
            line-height: 1.2;
            color: #333;
            word-break: break-word;
        }

        .error {
            color: #dc3545;
            font-style: italic;
        }

        .stats {
            margin-top: 20px;
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        @media (max-width: 768px) {
            .input-group {
                flex-direction: column;
                align-items: stretch;
            }

            .input-group input,
            .input-group button {
                width: 100%;
            }

            .font-table th:first-child,
            .font-table td:first-child {
                min-width: 150px;
            }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🔤 Font Preview Tool</h1>
        <div class="input-group">
            <label for="previewText">Text to preview:</label>
            <input type="text" id="previewText" placeholder="Enter your text here..." value="Wanted">
            <button onclick="generatePreview()">Generate Preview</button>
        </div>
        <div class="font-size-controls">
            <label for="fontSize">Font Size:</label>
            <input type="range" id="fontSize" min="12" max="72" value="24" oninput="updateFontSize(this.value)">
            <span id="fontSizeValue">24px</span>
        </div>
    </div>

    <div id="loadingMessage" class="loading" style="display: none;">
        Loading fonts and generating preview...
    </div>

    <div id="fontTableContainer" style="display: none;">
        <div class="font-table">
            <table>
                <thead>
                    <tr>
                        <th>Font Name</th>
                        <th>Preview</th>
                    </tr>
                </thead>
                <tbody id="fontTableBody">
                </tbody>
            </table>
        </div>
        <div class="stats" id="statsContainer"></div>
    </div>

    <script src="font-map.js"></script>
    <script>
        let currentFontSize = 24;

        function updateFontSize(size) {
            currentFontSize = size;
            document.getElementById('fontSizeValue').textContent = size + 'px';
            
            // Update all existing previews
            const previews = document.querySelectorAll('.font-preview');
            previews.forEach(preview => {
                preview.style.fontSize = size + 'px';
            });
        }

        function generatePreview() {
            const text = document.getElementById('previewText').value.trim();
            
            if (!text) {
                alert('Please enter some text to preview!');
                return;
            }

            // Show loading
            document.getElementById('loadingMessage').style.display = 'block';
            document.getElementById('fontTableContainer').style.display = 'none';

            // Clear previous results
            const tableBody = document.getElementById('fontTableBody');
            tableBody.innerHTML = '';

            // Generate preview after a short delay to show loading
            setTimeout(() => {
                generateFontPreviews(text);
            }, 100);
        }

        function generateFontPreviews(text) {
            const tableBody = document.getElementById('fontTableBody');
            let successCount = 0;
            let errorCount = 0;

            // Get all font families from the fontMap
            const fontFamilies = Object.keys(fontMap);
            
            fontFamilies.forEach(fontFamily => {
                const fontData = fontMap[fontFamily];
                
                // Use regular variant if available, otherwise use the first available variant
                let fontPath = fontData.regular || fontData[Object.keys(fontData)[0]];
                
                if (fontPath) {
                    const row = document.createElement('tr');
                    
                    // Font name cell
                    const nameCell = document.createElement('td');
                    nameCell.className = 'font-name';
                    nameCell.textContent = fontFamily;
                    
                    // Preview cell
                    const previewCell = document.createElement('td');
                    const previewDiv = document.createElement('div');
                    previewDiv.className = 'font-preview';
                    previewDiv.style.fontSize = currentFontSize + 'px';
                    previewDiv.textContent = text;
                    
                    // Load the font
                    loadFont(fontFamily, fontPath).then(() => {
                        previewDiv.style.fontFamily = `"${fontFamily}", Arial, sans-serif`;
                        successCount++;
                        updateStats(successCount, errorCount, fontFamilies.length);
                    }).catch(() => {
                        previewDiv.innerHTML = `<span class="error">Font failed to load</span>`;
                        errorCount++;
                        updateStats(successCount, errorCount, fontFamilies.length);
                    });
                    
                    previewCell.appendChild(previewDiv);
                    row.appendChild(nameCell);
                    row.appendChild(previewCell);
                    tableBody.appendChild(row);
                } else {
                    errorCount++;
                }
            });

            // Hide loading and show results
            document.getElementById('loadingMessage').style.display = 'none';
            document.getElementById('fontTableContainer').style.display = 'block';
            
            updateStats(successCount, errorCount, fontFamilies.length);
        }

        function loadFont(fontFamily, fontPath) {
            return new Promise((resolve, reject) => {
                const font = new FontFace(fontFamily, `url(${fontPath})`);
                
                font.load().then(() => {
                    document.fonts.add(font);
                    resolve();
                }).catch(() => {
                    reject();
                });
            });
        }

        function updateStats(successCount, errorCount, totalCount) {
            const statsContainer = document.getElementById('statsContainer');
            statsContainer.innerHTML = `
                <strong>Preview Stats:</strong> 
                ${successCount} fonts loaded successfully, 
                ${errorCount} failed to load, 
                ${totalCount} total fonts
            `;
        }

        // Generate preview on Enter key
        document.getElementById('previewText').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                generatePreview();
            }
        });

        // Auto-generate preview on page load
        window.addEventListener('load', function() {
            generatePreview();
        });
    </script>
</body>
</html>
