<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG to SVG Vectorizer</title>
    
    <!-- Tailwind CSS for styling -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- ImageTracer.js for vectorization -->
    <!-- This library provides algorithms similar to Potrace and AutoTrace -->
    <script type="text/javascript" src="https://cdn.jsdelivr.net/gh/jankovicsandras/imagetracerjs@1.2.6/imagetracer.min.js"></script>

    <style>
        /* Custom styles for a better look and feel */
        body {
            font-family: 'Inter', sans-serif;
        }
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;700&display=swap');

        /* Custom slider styles */
        input[type="range"] {
            -webkit-appearance: none;
            appearance: none;
            width: 100%;
            height: 8px;
            background: #d1d5db; /* gray-300 */
            border-radius: 9999px;
            outline: none;
            opacity: 0.7;
            transition: opacity .2s;
        }
        input[type="range"]:hover {
            opacity: 1;
        }
        input[type="range"]::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            background: #3b82f6; /* blue-500 */
            cursor: pointer;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 5px rgba(0,0,0,0.2);
        }
        input[type="range"]::-moz-range-thumb {
            width: 20px;
            height: 20px;
            background: #3b82f6; /* blue-500 */
            cursor: pointer;
            border-radius: 50%;
            border: 2px solid white;
            box-shadow: 0 0 5px rgba(0,0,0,0.2);
        }
        .loader {
            border: 4px solid #f3f3f3; /* Light grey */
            border-top: 4px solid #3b82f6; /* Blue */
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .modal-backdrop {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background-color: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 50;
        }
    </style>
</head>
<body class="bg-gray-100 text-gray-800">

    <div id="app" class="container mx-auto p-4 md:p-8">
        
        <!-- Header -->
        <header class="text-center mb-8">
            <h1 class="text-4xl md:text-5xl font-bold text-gray-900">PNG to SVG Vectorizer</h1>
            <p class="mt-2 text-lg text-gray-600 max-w-3xl mx-auto">Upload a PNG, choose an algorithm, and convert your pixel-based image into a scalable vector graphic (SVG).</p>
        </header>

        <main class="grid grid-cols-1 lg:grid-cols-12 gap-8">

            <!-- Left Column: Controls -->
            <div class="lg:col-span-4 bg-white p-6 rounded-2xl shadow-lg h-fit">
                <h2 class="text-2xl font-bold mb-4 border-b pb-2">Controls</h2>
                
                <!-- File Uploader -->
                <div class="mb-6">
                    <label for="image-uploader" class="block text-sm font-medium text-gray-700 mb-2">1. Upload PNG Image</label>
                    <input type="file" id="image-uploader" accept="image/png" class="block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100 cursor-pointer"/>
                </div>

                <!-- Algorithm Selection -->
                <div class="mb-6">
                    <h3 class="block text-sm font-medium text-gray-700 mb-2">2. Choose Algorithm</h3>
                    <div id="algorithm-selector" class="space-y-3">
                        <div>
                            <input type="radio" id="algo-potrace" name="algorithm" value="potrace" class="hidden peer" checked>
                            <label for="algo-potrace" class="block p-3 rounded-lg border border-gray-300 cursor-pointer peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500 transition-all">
                                <p class="font-semibold text-gray-800">Potrace (Edge Tracing)</p>
                                <p class="text-xs text-gray-500 mt-1">Best for logos & simple graphics. Creates smooth black & white vector paths.</p>
                            </label>
                        </div>
                        <div>
                            <input type="radio" id="algo-autotrace" name="algorithm" value="autotrace" class="hidden peer">
                            <label for="algo-autotrace" class="block p-3 rounded-lg border border-gray-300 cursor-pointer peer-checked:border-blue-500 peer-checked:ring-2 peer-checked:ring-blue-500 transition-all">
                                <p class="font-semibold text-gray-800">AutoTrace (Color)</p>
                                <p class="text-xs text-gray-500 mt-1">Good for complex images. Reduces colors and traces shapes. Supports multiple colors.</p>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Vectorization Controls -->
                <div class="mb-6">
                    <h3 class="block text-sm font-medium text-gray-700 mb-2">3. Adjust Settings</h3>
                    <!-- Potrace/Threshold Controls -->
                    <div id="potrace-controls" class="space-y-4">
                        <div>
                            <label for="threshold" class="flex justify-between text-sm text-gray-600">
                                <span>Threshold</span>
                                <span id="threshold-value">128</span>
                            </label>
                            <input type="range" id="threshold" min="0" max="255" value="128" class="w-full mt-1">
                        </div>
                    </div>
                    <!-- AutoTrace Controls -->
                    <div id="autotrace-controls" class="space-y-4 hidden">
                        <div>
                            <label for="colors" class="flex justify-between text-sm text-gray-600">
                                <span>Number of Colors</span>
                                <span id="colors-value">8</span>
                            </label>
                            <input type="range" id="colors" min="2" max="32" value="8" class="w-full mt-1">
                        </div>
                    </div>
                     <!-- Common Controls -->
                    <div class="space-y-4 mt-4">
                       <div>
                            <label for="smoothing" class="flex justify-between text-sm text-gray-600">
                                <span>Smoothing (Despeckle)</span>
                                <span id="smoothing-value">2</span>
                            </label>
                            <input type="range" id="smoothing" min="0" max="20" value="2" class="w-full mt-1">
                        </div>
                    </div>
                </div>

                <!-- Action Button -->
                <button id="vectorize-btn" class="w-full bg-blue-600 text-white font-bold py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-4 focus:ring-blue-300 transition-all disabled:bg-gray-400 disabled:cursor-not-allowed flex items-center justify-center" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M5 4a2 2 0 00-2 2v6a2 2 0 002 2h10a2 2 0 002-2V6a2 2 0 00-2-2H5zm10 1H5a1 1 0 00-1 1v6a1 1 0 001 1h10a1 1 0 001-1V6a1 1 0 00-1-1z" /><path d="M6 9a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7A.5.5 0 016 9zm0 2a.5.5 0 01.5-.5h7a.5.5 0 010 1h-7a.5.5 0 01-.5-.5z" /></svg>
                    Vectorize
                </button>
            </div>

            <!-- Right Column: Previews & Export -->
            <div class="lg:col-span-8">
                <div class="bg-white p-6 rounded-2xl shadow-lg">
                    <h2 class="text-2xl font-bold mb-4 border-b pb-2">Preview</h2>
                    <div id="preview-area" class="grid grid-cols-1 md:grid-cols-2 gap-4 min-h-[300px] bg-gray-200/50 p-4 rounded-lg border-dashed border-2 border-gray-300">
                        <!-- Original Image -->
                        <div class="text-center">
                            <h3 class="font-semibold mb-2">Original PNG</h3>
                            <div id="original-preview-container" class="w-full h-full flex items-center justify-center bg-white rounded-md shadow-inner min-h-[250px]">
                                <p id="original-placeholder" class="text-gray-500">Upload an image to start</p>
                                <img id="original-image" class="max-w-full max-h-[400px] object-contain hidden" />
                            </div>
                        </div>
                        <!-- Vectorized SVG -->
                        <div class="text-center">
                            <h3 class="font-semibold mb-2">Vectorized SVG</h3>
                            <div id="vectorized-preview-container" class="w-full h-full flex items-center justify-center bg-white rounded-md shadow-inner min-h-[250px] relative">
                                <p id="vectorized-placeholder" class="text-gray-500">Result will appear here</p>
                                <div id="loader" class="loader hidden"></div>
                                <div id="vectorized-image-wrapper" class="w-full h-full hidden"></div>
                            </div>
                        </div>
                    </div>
                </div>

                <div id="export-section" class="bg-white p-6 rounded-2xl shadow-lg mt-8 hidden">
                    <h2 class="text-2xl font-bold mb-4 border-b pb-2">Export</h2>
                    <div class="flex flex-wrap gap-4">
                        <button id="download-btn" class="flex-1 bg-green-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-green-700 focus:outline-none focus:ring-4 focus:ring-green-300 transition-all flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" /></svg>
                            Download SVG
                        </button>
                        <button id="copy-btn" class="flex-1 bg-purple-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-purple-700 focus:outline-none focus:ring-4 focus:ring-purple-300 transition-all flex items-center justify-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"><path d="M8 3a1 1 0 011-1h2a1 1 0 110 2H9a1 1 0 01-1-1z" /><path d="M6 3a2 2 0 00-2 2v11a2 2 0 002 2h8a2 2 0 002-2V5a2 2 0 00-2-2 3 3 0 01-3 3H9a3 3 0 01-3-3z" /></svg>
                            Copy Code
                        </button>
                        <button id="view-source-btn" class="flex-1 bg-gray-600 text-white font-bold py-2 px-4 rounded-lg hover:bg-gray-700 focus:outline-none focus:ring-4 focus:ring-gray-300 transition-all flex items-center justify-center">
                           <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M10 20l4-16m4 4l4 4-4 4M6 16l-4-4 4-4" /></svg>
                            View Source
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- SVG Source Code Modal -->
    <div id="source-modal" class="modal-backdrop hidden">
        <div class="bg-white rounded-lg shadow-xl w-11/12 max-w-4xl max-h-[80vh] flex flex-col">
            <div class="p-4 border-b flex justify-between items-center">
                <h3 class="text-xl font-bold">SVG Source Code</h3>
                <button id="close-modal-btn" class="text-gray-500 hover:text-gray-800">&times;</button>
            </div>
            <div class="p-4 overflow-y-auto">
                <pre class="bg-gray-900 text-white p-4 rounded-md text-sm"><code id="svg-source-code"></code></pre>
            </div>
        </div>
    </div>
    
    <!-- Notification element -->
    <div id="notification" class="fixed bottom-5 right-5 bg-green-500 text-white py-2 px-4 rounded-lg shadow-lg transform translate-y-20 transition-transform duration-300">
        Copied to clipboard!
    </div>


    <script>
        function initializeApp() {
            // DOM Elements
            const imageUploader = document.getElementById('image-uploader');
            const vectorizeBtn = document.getElementById('vectorize-btn');
            const originalImage = document.getElementById('original-image');
            const originalPlaceholder = document.getElementById('original-placeholder');
            const vectorizedImageWrapper = document.getElementById('vectorized-image-wrapper');
            const vectorizedPlaceholder = document.getElementById('vectorized-placeholder');
            const loader = document.getElementById('loader');
            const exportSection = document.getElementById('export-section');
            const downloadBtn = document.getElementById('download-btn');
            const copyBtn = document.getElementById('copy-btn');
            const viewSourceBtn = document.getElementById('view-source-btn');
            const sourceModal = document.getElementById('source-modal');
            const closeModalBtn = document.getElementById('close-modal-btn');
            const svgSourceCode = document.getElementById('svg-source-code');
            const notification = document.getElementById('notification');

            // Controls
            const algorithmSelector = document.getElementById('algorithm-selector');
            const potraceControls = document.getElementById('potrace-controls');
            const autotraceControls = document.getElementById('autotrace-controls');
            
            const thresholdSlider = document.getElementById('threshold');
            const thresholdValue = document.getElementById('threshold-value');
            const smoothingSlider = document.getElementById('smoothing');
            const smoothingValue = document.getElementById('smoothing-value');
            const colorsSlider = document.getElementById('colors');
            const colorsValue = document.getElementById('colors-value');
            
            let uploadedImageDataUrl = null;
            let lastSvgData = '';

            // --- Event Listeners ---

            // Image Uploader
            imageUploader.addEventListener('change', (event) => {
                const file = event.target.files[0];
                if (file && file.type === 'image/png') {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        uploadedImageDataUrl = e.target.result;
                        originalImage.src = uploadedImageDataUrl;
                        originalImage.classList.remove('hidden');
                        originalPlaceholder.classList.add('hidden');
                        vectorizeBtn.disabled = false;
                        vectorizeBtn.innerText = 'Vectorize';
                        exportSection.classList.add('hidden');
                        vectorizedImageWrapper.classList.add('hidden');
                        vectorizedPlaceholder.classList.remove('hidden');
                    };
                    reader.onerror = (e) => {
                        console.error('FileReader error:', e.target.error);
                        showNotification('Error reading the file.', true);
                    };
                    reader.readAsDataURL(file);
                } else if (file) { // if a file is selected but it's not a PNG
                    showNotification('Please upload a valid PNG file.', true);
                    imageUploader.value = ''; // Reset the input
                }
            });

            // Algorithm selection
            algorithmSelector.addEventListener('change', (e) => {
                const selectedAlgo = e.target.value;
                if (selectedAlgo === 'potrace') {
                    potraceControls.classList.remove('hidden');
                    autotraceControls.classList.add('hidden');
                } else {
                    potraceControls.classList.add('hidden');
                    autotraceControls.classList.remove('hidden');
                }
            });
            
            // Slider value displays
            thresholdSlider.addEventListener('input', () => thresholdValue.textContent = thresholdSlider.value);
            smoothingSlider.addEventListener('input', () => smoothingValue.textContent = smoothingSlider.value);
            colorsSlider.addEventListener('input', () => colorsValue.textContent = colorsSlider.value);

            // Vectorize Button
            vectorizeBtn.addEventListener('click', () => {
                if (!uploadedImageDataUrl) return;

                loader.classList.remove('hidden');
                vectorizedPlaceholder.classList.add('hidden');
                vectorizedImageWrapper.classList.add('hidden');
                vectorizedImageWrapper.innerHTML = '';
                exportSection.classList.add('hidden');
                vectorizeBtn.disabled = true;

                setTimeout(() => {
                    const options = getVectorizationOptions();
                    ImageTracer.imageToSVG(
                        uploadedImageDataUrl,
                        (svgString) => {
                            lastSvgData = svgString;
                            displaySVG(svgString);
                            loader.classList.add('hidden');
                            vectorizeBtn.disabled = false;
                            vectorizeBtn.innerText = 'Re-Vectorize';
                        },
                        options
                    );
                }, 50);
            });
            
            // Export Buttons
            downloadBtn.addEventListener('click', () => {
                const blob = new Blob([lastSvgData], { type: 'image/svg+xml' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = 'vectorized_image.svg';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
            });

            copyBtn.addEventListener('click', () => {
                const textarea = document.createElement('textarea');
                textarea.value = lastSvgData;
                document.body.appendChild(textarea);
                textarea.select();
                try {
                    document.execCommand('copy');
                    showNotification('Copied to clipboard!');
                } catch (err) {
                    showNotification('Failed to copy!', true);
                }
                document.body.removeChild(textarea);
            });

            viewSourceBtn.addEventListener('click', () => {
                svgSourceCode.textContent = lastSvgData;
                sourceModal.classList.remove('hidden');
            });

            closeModalBtn.addEventListener('click', () => {
                sourceModal.classList.add('hidden');
            });
            sourceModal.addEventListener('click', (e) => {
                if (e.target === sourceModal) {
                    sourceModal.classList.add('hidden');
                }
            });

            // --- Helper Functions ---

            function getVectorizationOptions() {
                const selectedAlgo = document.querySelector('input[name="algorithm"]:checked').value;
                const options = {
                    "ltres": 1, "qtres": 1, "pathomit": 8, "rightangleenhance": true,
                    "strokewidth": 1, "blurradius": 0, "blurdelta": 20,
                    "despeckle": true,
                    "despecklelevel": parseInt(smoothingSlider.value, 10),
                    "despecklesize": parseInt(smoothingSlider.value, 10),
                };

                if (selectedAlgo === 'potrace') {
                    options.numberofcolors = 2;
                    options.turdsize = parseInt(smoothingSlider.value, 10);
                    options.ltres = parseInt(thresholdSlider.value, 10);
                    options.qtres = parseInt(thresholdSlider.value, 10);
                    options.colorsampling = 0;
                } else {
                    options.numberofcolors = parseInt(colorsSlider.value, 10);
                    options.colorsampling = 2;
                    options.mincolorratio = 0.02;
                }
                
                return options;
            }
            
            function displaySVG(svgString) {
                vectorizedImageWrapper.innerHTML = svgString;
                const svgElement = vectorizedImageWrapper.querySelector('svg');
                if (svgElement) {
                    svgElement.style.width = '100%';
                    svgElement.style.height = '100%';
                    svgElement.style.objectFit = 'contain';
                }
                vectorizedImageWrapper.classList.remove('hidden');
                exportSection.classList.remove('hidden');
            }
            
            let notificationTimeout;
            function showNotification(message, isError = false) {
                notification.textContent = message;
                notification.className = `fixed bottom-5 right-5 text-white py-2 px-4 rounded-lg shadow-lg transform transition-transform duration-300 ${isError ? 'bg-red-500' : 'bg-green-500'}`;
                notification.classList.remove('translate-y-20');

                clearTimeout(notificationTimeout);
                notificationTimeout = setTimeout(() => {
                    notification.classList.add('translate-y-20');
                }, 3000);
            }
        }

        // This function checks if the ImageTracer library is available.
        // If it is, it calls initializeApp(). If not, it waits and checks again.
        function checkAndInit() {
            if (typeof ImageTracer !== 'undefined') {
                initializeApp();
            } else {
                setTimeout(checkAndInit, 100);
            }
        }

        // Start the process.
        checkAndInit();
    </script>
</body>
</html>
