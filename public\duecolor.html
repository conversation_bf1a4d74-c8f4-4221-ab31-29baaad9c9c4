<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Duotone Color Filter</title>
  <style>
    /* Basic body styling */
    body {
      margin: 0;
      font-family: 'Inter', sans-serif; /* Using Inter font as per guidelines */
      color: #333;
      overflow: hidden; /* Prevent scrolling */
    }

    /* Container for the background image and filter */
    div {
      width: 100vw;
      height: 100vh;
      background-image: url(https://images.unsplash.com/photo-1477554193778-9562c28588c0?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=crop&w=1050&q=80);
      background-repeat: no-repeat;
      background-size: cover;
      background-position: center;
      transform: scaleX(-1); /* Flips the image horizontally */
      -webkit-filter: url("#duotone"); /* Apply SVG filter for WebKit browsers */
      filter: url("#duotone"); /* Apply SVG filter for other browsers */
    }

    /* Styling for the input form */
    form {
      position: fixed; /* Keep form fixed on screen */
      top: 20px;
      left: 20px;
      z-index: 10; /* Ensure form is above the background */
      background-color: rgba(255, 255, 255, 0.7); /* Semi-transparent white background */
      padding: 30px;
      border-radius: 15px; /* Rounded corners for the form */
      box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1); /* Subtle shadow */
    }

    /* Legend styling */
    legend {
      font-weight: bold;
      margin-bottom: 20px;
      border-bottom: 1px solid #333;
      padding-bottom: 8px;
      font-size: 1.2em;
    }

    /* Label and input display */
    label, input {
      display: block;
      margin-bottom: 10px;
    }

    /* Padding for labels and legend */
    label,
    legend {
      padding-left: 8px;
    }

    /* Input field styling */
    input {
      padding: 10px 15px;
      border: 1px solid #333;
      border-radius: 25px; /* Pill-shaped input fields */
      background-color: transparent;
      margin-top: 5px;
      margin-bottom: 15px;
      width: 200px; /* Fixed width for inputs */
    }

    /* Remove bottom margin for the last input */
    input:last-of-type {
      margin-bottom: 0;
    }

    /* Focus state for input fields */
    input:focus {
      outline: none;
      background-color: rgba(255, 255, 255, 0.3); /* Slightly more opaque on focus */
      box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.5); /* Blue glow on focus */
    }

    /* Responsive adjustments */
    @media (max-width: 600px) {
      form {
        top: 10px;
        left: 10px;
        padding: 20px;
      }
      input {
        width: 150px;
      }
    }
  </style>
</head>
<body>
  <main>
    <form>
      <legend>Enter HEX colors</legend>
      <label for="color1">Color 1 (e.g., #FF8C00)</label>
      <input type="text" id="color1" name="color1" value="#FF8C00"> <!-- Default value -->
      <label for="color2">Color 2 (e.g., #4682B4)</label>
      <input type="text" id="color2" name="color2" value="#4682B4"> <!-- Default value -->
    </form>
    <div>
      <!-- SVG filter definition, hidden from view -->
      <svg class="defs-only" style="visibility: hidden; position: absolute;">
        <filter color-interpolation-filters="srgb" x="0" y="0" height="100%" width="100%" id="duotone">
          <!-- fecolormatrix is where the magic happens for duotone effect -->
          <fecolormatrix id="fe" type="matrix" values="0.375 0 0 0 0.3515625 0.83203125 0 0 0 0.1640625 0.40234375 0 0 0 0.5234375 0 0 0 1 0">
          </fecolormatrix>
        </filter>
      </svg>
    </div>
  </main>

  <!-- jQuery library for DOM manipulation and event handling -->
  <script src="https://ajax.googleapis.com/ajax/libs/jquery/3.5.1/jquery.min.js"></script>
  <script>
    // hexToRgb function: Converts a HEX color string to an RGB array [r, g, b]
    function hexToRgb(hex) {
        hex = $.trim(hex); // Remove leading/trailing whitespace
        hex = hex.replace('#', ''); // Remove the '#' character
        var bigint = parseInt(hex, 16); // Parse the hex string to an integer
        var r = (bigint >> 16) & 255; // Extract red component
        var g = (bigint >> 8) & 255;  // Extract green component
        var b = bigint & 255;         // Extract blue component
        return [r, g, b];
    }

    // duotone function: Generates a color matrix string for the SVG filter
    // based on two input HEX colors.
    function duotone(col1, col2) {
        var color1 = hexToRgb(col1); // Convert first color to RGB
        var color2 = hexToRgb(col2); // Convert second color to RGB

        // Normalize RGB values to 0-1 range
        var c1 = {
            red: color1[0] / 256,
            green: color1[1] / 256,
            blue: color1[2] / 256
        };

        var c2 = {
            red: color2[0] / 256,
            green: color2[1] / 256,
            blue: color2[2] / 256
        };

        // Construct the 5x4 color matrix for the fecolormatrix filter
        // The matrix transforms the input colors to the duotone effect
        var value = [];
        // Red row: (c1.red - c2.red) * R + 0*G + 0*B + 0*A + c2.red (offset)
        value = value.concat([c1.red - c2.red, 0, 0, 0, c2.red]);
        // Green row: 0*R + (c1.green - c2.green) * G + 0*B + 0*A + c2.green (offset)
        value = value.concat([c1.green - c2.green, 0, 0, 0, c2.green]);
        // Blue row: 0*R + 0*G + (c1.blue - c2.blue) * B + 0*A + c2.blue (offset)
        value = value.concat([c1.blue - c2.blue, 0, 0, 0, c2.blue]);
        // Alpha row: 0*R + 0*G + 0*B + 1*A + 0 (preserve alpha)
        value = value.concat([0, 0, 0, 1, 0]);

        return value.join(' '); // Return the matrix values as a space-separated string
    }

    // ApplyColorMatrix function: Reads input values, generates matrix, and updates SVG filter
    function ApplyColorMatrix() {
      var col1 = $("#color1").val(); // Get value from first color input
      var col2 = $("#color2").val(); // Get value from second color input
      var colormatrix = duotone(col1, col2); // Generate the new color matrix
      $("#fe").attr("values", colormatrix); // Set the 'values' attribute of the fecolormatrix element
    }

    // Initial application of the filter when the page loads
    // This ensures the duotone effect is visible immediately with default colors
    $(document).ready(function() {
      ApplyColorMatrix();
    });

    // Bind the ApplyColorMatrix function to the 'input' event of both color fields
    // This makes the filter update in real-time as the user types
    $("#color1, #color2").on('input', ApplyColorMatrix);
  </script>
</body>
</html>
