<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Preview Generation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .debug-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .debug-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .debug-button:hover {
            background: #2563EB;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .result.error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .result.info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
        }
        .preview-container {
            display: flex;
            gap: 20px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        .preview-box {
            border: 2px solid #ddd;
            padding: 10px;
            border-radius: 8px;
            text-align: center;
            min-width: 200px;
        }
        .preview-image {
            max-width: 300px;
            max-height: 300px;
            border: 1px solid #ccc;
        }
        h1 {
            color: #1F2937;
            text-align: center;
        }
        h3 {
            color: #374151;
            margin-bottom: 10px;
        }
        .instructions {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            color: #92400E;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
        .code-block {
            background: #1f2937;
            color: #f9fafb;
            padding: 15px;
            border-radius: 6px;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>🔍 Preview Generation Debug Tool</h1>
    
    <div class="instructions">
        <strong>Debug Instructions:</strong><br>
        1. Open the design editor in another tab<br>
        2. Add content (text, images) to the artboard<br>
        3. Use these tools to debug the preview generation process<br>
        4. Check console logs for detailed debugging information
    </div>

    <div class="debug-section">
        <h3>Step 1: Check Canvas Element</h3>
        <p>Verify that the main canvas element exists and has content.</p>
        <button class="debug-button" onclick="checkCanvasElement()">Check Canvas Element</button>
        <div id="canvasResult" class="result"></div>
    </div>

    <div class="debug-section">
        <h3>Step 2: Check Canvas Objects</h3>
        <p>Verify canvas objects and their loading status.</p>
        <button class="debug-button" onclick="checkCanvasObjects()">Check Canvas Objects</button>
        <div id="objectsResult" class="result"></div>
    </div>

    <div class="debug-section">
        <h3>Step 3: Test Preview Generation</h3>
        <p>Generate a preview using the same method as the save function.</p>
        <button class="debug-button" onclick="testPreviewGeneration()">Test Preview Generation</button>
        <div id="previewResult" class="result"></div>
        <div class="preview-container">
            <div class="preview-box">
                <h4>Generated Preview</h4>
                <img id="generatedPreview" class="preview-image" style="display: none;">
            </div>
        </div>
    </div>

    <div class="debug-section">
        <h3>Step 4: Manual Canvas Capture</h3>
        <p>Capture the canvas directly without any processing.</p>
        <button class="debug-button" onclick="captureCanvasDirectly()">Capture Canvas Directly</button>
        <div id="captureResult" class="result"></div>
        <div class="preview-container">
            <div class="preview-box">
                <h4>Direct Canvas Capture</h4>
                <img id="directCapture" class="preview-image" style="display: none;">
            </div>
        </div>
    </div>

    <script>
        function checkCanvasElement() {
            const result = document.getElementById('canvasResult');
            
            try {
                // Try to access the design editor window
                const designWindow = window.parent || window;
                
                // Check for canvas element
                const canvas = document.getElementById('demo') || designWindow.document?.getElementById('demo');
                
                if (canvas) {
                    const report = `
CANVAS ELEMENT FOUND:
- Element: ${canvas.tagName}
- ID: ${canvas.id}
- Width: ${canvas.width}
- Height: ${canvas.height}
- Style Width: ${canvas.style.width}
- Style Height: ${canvas.style.height}
- Client Width: ${canvas.clientWidth}
- Client Height: ${canvas.clientHeight}
- Context Available: ${!!canvas.getContext('2d')}

Canvas element is accessible and ready for capture.
                    `;
                    result.textContent = report;
                    result.className = 'result success';
                } else {
                    const report = `
CANVAS ELEMENT NOT FOUND:

This debug tool needs to be run from the design editor context.

To debug properly:
1. Open design editor: http://localhost:3006/design-editor.html
2. Open browser console (F12)
3. Run these commands:

const canvas = document.getElementById('demo');
console.log('Canvas found:', !!canvas);
if (canvas) {
    console.log('Canvas size:', canvas.width, 'x', canvas.height);
    console.log('Canvas context:', !!canvas.getContext('2d'));
}
                    `;
                    result.textContent = report;
                    result.className = 'result error';
                }
            } catch (error) {
                result.textContent = 'Error: ' + error.message;
                result.className = 'result error';
            }
        }

        function checkCanvasObjects() {
            const result = document.getElementById('objectsResult');
            
            const instructions = `
CANVAS OBJECTS CHECK:

Run this in the design editor console:

console.log('Canvas Objects:', window.canvasObjects);
console.log('Artboard:', window.artboard);
console.log('Background Color:', window.canvasBackgroundColor);

// Check image objects specifically
const imageObjects = window.canvasObjects ? window.canvasObjects.filter(obj => obj.type === 'image') : [];
console.log('Image objects:', imageObjects.length);

imageObjects.forEach((obj, index) => {
    console.log('Image', index + 1, ':', {
        id: obj.id,
        imageUrl: obj.imageUrl,
        hasImage: !!obj.image,
        complete: obj.image ? obj.image.complete : false,
        naturalWidth: obj.image ? obj.image.naturalWidth : 0,
        loaded: obj.image && obj.image.complete && obj.image.naturalWidth > 0
    });
});

Expected: All images should show loaded: true
            `;
            
            result.textContent = instructions;
            result.className = 'result info';
        }

        function testPreviewGeneration() {
            const result = document.getElementById('previewResult');
            
            const instructions = `
PREVIEW GENERATION TEST:

Run this in the design editor console to simulate the save process:

// Simulate the preview generation
async function debugPreviewGeneration() {
    console.log('🔍 Starting debug preview generation...');
    
    // Check canvas
    const mainCanvas = document.getElementById('demo');
    console.log('Canvas found:', !!mainCanvas);
    if (!mainCanvas) {
        console.error('Canvas not found!');
        return;
    }
    
    // Check artboard
    console.log('Artboard:', window.artboard);
    if (!window.artboard) {
        console.error('Artboard not defined!');
        return;
    }
    
    // Create export canvas
    const exportCanvas = document.createElement('canvas');
    exportCanvas.width = window.artboard.width;
    exportCanvas.height = window.artboard.height;
    const exportCtx = exportCanvas.getContext('2d');
    
    // Setup clipping and translation
    exportCtx.save();
    exportCtx.beginPath();
    exportCtx.rect(0, 0, window.artboard.width, window.artboard.height);
    exportCtx.clip();
    exportCtx.translate(-window.artboard.x, -window.artboard.y);
    
    // Fill background
    exportCtx.fillStyle = window.canvasBackgroundColor || '#ffffff';
    exportCtx.fillRect(window.artboard.x, window.artboard.y, window.artboard.width, window.artboard.height);
    
    // Draw main canvas
    exportCtx.drawImage(mainCanvas, 0, 0);
    exportCtx.restore();
    
    // Generate preview
    const dataUrl = exportCanvas.toDataURL('image/png');
    console.log('Preview generated, length:', dataUrl.length);
    
    // Display preview
    const img = new Image();
    img.onload = () => console.log('Preview image loaded successfully');
    img.src = dataUrl;
    document.body.appendChild(img);
    
    return dataUrl;
}

debugPreviewGeneration();
            `;
            
            result.textContent = instructions;
            result.className = 'result info';
        }

        function captureCanvasDirectly() {
            const result = document.getElementById('captureResult');
            
            const instructions = `
DIRECT CANVAS CAPTURE:

Run this in the design editor console:

const canvas = document.getElementById('demo');
if (canvas) {
    const dataUrl = canvas.toDataURL('image/png');
    console.log('Direct capture length:', dataUrl.length);
    
    // Create and display image
    const img = new Image();
    img.onload = () => {
        console.log('Direct capture loaded successfully');
        console.log('Image dimensions:', img.width, 'x', img.height);
    };
    img.src = dataUrl;
    document.body.appendChild(img);
    
    // Also log a preview of the data URL
    console.log('Data URL preview:', dataUrl.substring(0, 100) + '...');
} else {
    console.error('Canvas not found for direct capture');
}

This will show you the entire canvas content without any cropping.
            `;
            
            result.textContent = instructions;
            result.className = 'result info';
        }
    </script>
</body>
</html>
