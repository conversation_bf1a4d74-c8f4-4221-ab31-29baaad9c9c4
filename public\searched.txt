D:\canvaapp\CascadeProjects\windsurf-project - tshirts\tshirts-repo1-ultimo-sinerror10-30-p-----4\public\js\design-editor.js
  13,45:    function setupHighDPICanvas(canvas, ctx, scaleFactor = 2) {
  14,68:        console.log(`🔧 [HIGH-DPI] Starting setupHighDPICanvas with scaleFactor: ${scaleFactor}`);
  14,83:        console.log(`🔧 [HIGH-DPI] Starting setupHighDPICanvas with scaleFactor: ${scaleFactor}`);
  21,33:        const totalScale = dpr * scaleFactor;
  22,84:        console.log(`🔧 [HIGH-DPI] Total scale factor: ${totalScale} (dpr: ${dpr} × scaleFactor: ${scaleFactor})`);
  22,99:        console.log(`🔧 [HIGH-DPI] Total scale factor: ${totalScale} (dpr: ${dpr} × scaleFactor: ${scaleFactor})`);
  59,15:        canvas.scaleFactor = totalScale;
  60,42:        console.log(`🔧 [HIGH-DPI] Stored scaleFactor on canvas: ${canvas.scaleFactor}`);
  60,74:        console.log(`🔧 [HIGH-DPI] Stored scaleFactor on canvas: ${canvas.scaleFactor}`);
  62,57:        console.log(`✅ [HIGH-DPI] Canvas enhanced with ${scaleFactor}x scaling (total: ${totalScale}x)`);
  68,15:    let currentScaleFactor = 2;
  69,61:    let canvasScale = setupHighDPICanvas(canvas, ctx, currentScaleFactor);
  233,19:    const offscreenScaleFactor = devicePixelRatio * qualityMultiplier;
  236,31:    os.width = 4096 * offscreenScaleFactor;
  237,32:    os.height = 4096 * offscreenScaleFactor;
  241,24:    octx.scale(offscreenScaleFactor, offscreenScaleFactor);
  241,46:    octx.scale(offscreenScaleFactor, offscreenScaleFactor);
  244,7:    os.scaleFactor = offscreenScaleFactor;
  244,30:    os.scaleFactor = offscreenScaleFactor;
  247,43:    tempWarpCanvas.width = 4096 * offscreenScaleFactor;
  248,44:    tempWarpCanvas.height = 4096 * offscreenScaleFactor;
  252,31:    tempWarpCtx.scale(offscreenScaleFactor, offscreenScaleFactor);
  252,53:    tempWarpCtx.scale(offscreenScaleFactor, offscreenScaleFactor);
  255,19:    tempWarpCanvas.scaleFactor = offscreenScaleFactor;
  255,42:    tempWarpCanvas.scaleFactor = offscreenScaleFactor;
  258,41:    letterCanvas.width = 2048 * offscreenScaleFactor;
  259,42:    letterCanvas.height = 2048 * offscreenScaleFactor;
  263,29:    letterCtx.scale(offscreenScaleFactor, offscreenScaleFactor);
  263,51:    letterCtx.scale(offscreenScaleFactor, offscreenScaleFactor);
  266,17:    letterCanvas.scaleFactor = offscreenScaleFactor;
  266,40:    letterCanvas.scaleFactor = offscreenScaleFactor;
  1588,48:            console.log(`🔤 [FONT-DEBUG] Canvas scaleFactor: ${targetCtx.canvas.scaleFactor}`);
  1588,80:            console.log(`🔤 [FONT-DEBUG] Canvas scaleFactor: ${targetCtx.canvas.scaleFactor}`);
  3886,25:                const minScaleFactor = Math.max(0.02, 0.5 - (perspectiveIntensity * 0.48));
  3890,51:                           'Min scale factor:', minScaleFactor);
  3913,81:                    // This creates a smooth transition from 1.0 (closest) to minScaleFactor (farthest)
  3915,43:                        scaleProgress = minScaleFactor;
  3919,43:                        scaleProgress = minScaleFactor + (scaleProgress - minScaleFactor) * blendFactor;
  3919,77:                        scaleProgress = minScaleFactor + (scaleProgress - minScaleFactor) * blendFactor;
  4746,20:        const renderScaleFactor = renderTransform.a; // Get the current scale factor
  4749,37:        targetCtx.setTransform(renderScaleFactor, 0, 0, renderScaleFactor, 0, 0);
  4749,62:        targetCtx.setTransform(renderScaleFactor, 0, 0, renderScaleFactor, 0, 0);
  5157,20:        const letterScaleFactor = letterTransform.a; // Get the current scale factor
  5160,37:        targetCtx.setTransform(letterScaleFactor, 0, 0, letterScaleFactor, 0, 0);
  5160,62:        targetCtx.setTransform(letterScaleFactor, 0, 0, letterScaleFactor, 0, 0);
  5440,89:            const metrics = renderStyledObjectToOffscreen(obj, octx, os.width / offscreenScaleFactor, os.height / offscreenScaleFactor);
  5440,123:            const metrics = renderStyledObjectToOffscreen(obj, octx, os.width / offscreenScaleFactor, os.height / offscreenScaleFactor);
  5468,59:                const osLogicalWidth = os.width / offscreenScaleFactor;
  5469,61:                const osLogicalHeight = os.height / offscreenScaleFactor;
  5472,58:                    targetCtx.drawImage(os, sx * offscreenScaleFactor, sy * offscreenScaleFactor, sw * offscreenScaleFactor, sh * offscreenScaleFactor, dx, dy, dw, dh);
  5472,85:                    targetCtx.drawImage(os, sx * offscreenScaleFactor, sy * offscreenScaleFactor, sw * offscreenScaleFactor, sh * offscreenScaleFactor, dx, dy, dw, dh);
  5472,112:                    targetCtx.drawImage(os, sx * offscreenScaleFactor, sy * offscreenScaleFactor, sw * offscreenScaleFactor, sh * offscreenScaleFactor, dx, dy, dw, dh);
  5472,139:                    targetCtx.drawImage(os, sx * offscreenScaleFactor, sy * offscreenScaleFactor, sw * offscreenScaleFactor, sh * offscreenScaleFactor, dx, dy, dw, dh);
  5484,62:                    targetCtx.drawImage(os, safeSx * offscreenScaleFactor, safeSy * offscreenScaleFactor, safeWidth * offscreenScaleFactor, safeHeight * offscreenScaleFactor,
  5484,93:                    targetCtx.drawImage(os, safeSx * offscreenScaleFactor, safeSy * offscreenScaleFactor, safeWidth * offscreenScaleFactor, safeHeight * offscreenScaleFactor,
  5484,127:                    targetCtx.drawImage(os, safeSx * offscreenScaleFactor, safeSy * offscreenScaleFactor, safeWidth * offscreenScaleFactor, safeHeight * offscreenScaleFactor,
  5484,162:                    targetCtx.drawImage(os, safeSx * offscreenScaleFactor, safeSy * offscreenScaleFactor, safeWidth * offscreenScaleFactor, safeHeight * offscreenScaleFactor,
  5622,69:        renderStyledObjectToOffscreen(obj, octx, os.width / offscreenScaleFactor, os.height / offscreenScaleFactor);
  5622,103:        renderStyledObjectToOffscreen(obj, octx, os.width / offscreenScaleFactor, os.height / offscreenScaleFactor);
  5627,18:        const warpScaleFactor = warpTransform.a;
  5628,37:        tempWarpCtx.setTransform(warpScaleFactor, 0, 0, warpScaleFactor, 0, 0);
  5628,60:        tempWarpCtx.setTransform(warpScaleFactor, 0, 0, warpScaleFactor, 0, 0);
  5629,63:        tempWarpCtx.clearRect(0, 0, tempWarpCanvas.width / warpScaleFactor, tempWarpCanvas.height / warpScaleFactor);
  5629,104:        tempWarpCtx.clearRect(0, 0, tempWarpCanvas.width / warpScaleFactor, tempWarpCanvas.height / warpScaleFactor);
  5636,52:        const sourceCenterY = (os.height / offscreenScaleFactor) / 2;
  5661,61:                const osLogicalHeight = os.height / offscreenScaleFactor;
  5663,63:                    const osLogicalWidth = os.width / offscreenScaleFactor;
  5664,81:                    const tempWarpLogicalWidth = tempWarpCanvas.width / offscreenScaleFactor;
  5665,83:                    const tempWarpLogicalHeight = tempWarpCanvas.height / offscreenScaleFactor;
  5667,81:                        (i * (osLogicalWidth / tempWarpLogicalWidth)) * offscreenScaleFactor,
  5668,38:                        sy * offscreenScaleFactor,
  5669,75:                        (osLogicalWidth / tempWarpLogicalWidth) * offscreenScaleFactor,
  5670,38:                        sh * offscreenScaleFactor,
  5700,14:        const scaleFactor = targetCtx.canvas.scaleFactor || 1; // Use same scale as main canvas
  5700,45:        const scaleFactor = targetCtx.canvas.scaleFactor || 1; // Use same scale as main canvas
  5705,25:        console.log('  - scaleFactor:', scaleFactor);
  5705,40:        console.log('  - scaleFactor:', scaleFactor);
  5708,42:        console.log('  - targetCtx.canvas.scaleFactor:', targetCtx.canvas.scaleFactor);
  5708,74:        console.log('  - targetCtx.canvas.scaleFactor:', targetCtx.canvas.scaleFactor);
  5712,42:        tempCanvas.width = logicalWidth * scaleFactor;
  5713,44:        tempCanvas.height = logicalHeight * scaleFactor;
  5718,25:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  5718,38:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  5718,90:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  5740,42:        textCanvas.width = logicalWidth * scaleFactor;
  5741,44:        textCanvas.height = logicalHeight * scaleFactor;
  5747,25:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  5747,38:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  5747,90:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  6234,56:        console.log('🔍 CIRCULAR SHADOW DEBUG: offscreenScaleFactor:', offscreenScaleFactor);
  6234,80:        console.log('🔍 CIRCULAR SHADOW DEBUG: offscreenScaleFactor:', offscreenScaleFactor);
  6273,82:                console.log('🔍 CIRCULAR SHADOW DEBUG: Quality setting - offscreenScaleFactor:', offscreenScaleFactor);
  6273,106:                console.log('🔍 CIRCULAR SHADOW DEBUG: Quality setting - offscreenScaleFactor:', offscreenScaleFactor);
  6372,42:        console.log('  - targetCtx.canvas.scaleFactor:', targetCtx.canvas.scaleFactor);
  6372,74:        console.log('  - targetCtx.canvas.scaleFactor:', targetCtx.canvas.scaleFactor);
  6380,14:        const scaleFactor = targetCtx.canvas.scaleFactor || 1; // Use same scale as main canvas
  6380,45:        const scaleFactor = targetCtx.canvas.scaleFactor || 1; // Use same scale as main canvas
  6383,42:        tempCanvas.width = logicalWidth * scaleFactor;
  6384,44:        tempCanvas.height = logicalHeight * scaleFactor;
  6386,25:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  6386,38:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  6386,90:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  6396,25:        console.log('  - scaleFactor:', scaleFactor);
  6396,40:        console.log('  - scaleFactor:', scaleFactor);
  6408,39:        offscreenCanvas.width = 2000 * scaleFactor;
  6409,40:        offscreenCanvas.height = 2000 * scaleFactor;
  6411,27:        offscreenCtx.scale(scaleFactor, scaleFactor);
  6411,40:        offscreenCtx.scale(scaleFactor, scaleFactor);
  6453,42:        textCanvas.width = logicalWidth * scaleFactor;
  6454,44:        textCanvas.height = logicalHeight * scaleFactor;
  6456,25:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  6456,38:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  6456,90:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  6507,58:        console.log('🔍 CIRCULAR GRADIENT DEBUG: offscreenScaleFactor:', offscreenScaleFactor);
  6507,82:        console.log('🔍 CIRCULAR GRADIENT DEBUG: offscreenScaleFactor:', offscreenScaleFactor);
  7803,26:                    const scaleFactor = transform.a; // Current scale factor from high-DPI setup
  7804,90:                    console.log(`🔄 CIRCULAR GRADIENT SCALE ${i}: Current scale factor: ${scaleFactor}`);
  7807,54:                    const gradientLength = diameter * scaleFactor;
  7831,26:                    const scaleFactor = transform.a; // Current scale factor from high-DPI setup
  7832,50:                    const scaledRadius = radius * scaleFactor;
  8208,116:            const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width / offscreenScaleFactor, letterCanvas.height / offscreenScaleFactor, i, chars.length);
  8208,160:            const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width / offscreenScaleFactor, letterCanvas.height / offscreenScaleFactor, i, chars.length);
  8284,14:        const scaleFactor = targetCtx.canvas.scaleFactor || 4; // Use same scale as main canvas
  8284,45:        const scaleFactor = targetCtx.canvas.scaleFactor || 4; // Use same scale as main canvas
  8285,34:        tempCanvas.width = 2000 * scaleFactor;
  8286,35:        tempCanvas.height = 2000 * scaleFactor;
  8288,25:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  8288,38:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  8288,90:        // tempCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  8324,116:            const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width / offscreenScaleFactor, letterCanvas.height / offscreenScaleFactor, i, chars.length);
  8324,160:            const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width / offscreenScaleFactor, letterCanvas.height / offscreenScaleFactor, i, chars.length);
  8375,34:        textCanvas.width = 2000 * scaleFactor;
  8376,35:        textCanvas.height = 2000 * scaleFactor;
  8378,25:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  8378,38:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  8378,90:        // textCtx.scale(scaleFactor, scaleFactor); // REMOVED: Canvas already sized with scaleFactor
  8422,116:            const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width / offscreenScaleFactor, letterCanvas.height / offscreenScaleFactor, i, maskChars.length);
  8422,160:            const letterInfo = renderSingleStyledLetter(letterObj, letter, letterCtx, letterCanvas.width / offscreenScaleFactor, letterCanvas.height / offscreenScaleFactor, i, maskChars.length);
  8645,14:        const scaleFactor = targetCtx.canvas.scaleFactor || 4; // Use same scale as main canvas
  8645,45:        const scaleFactor = targetCtx.canvas.scaleFactor || 4; // Use same scale as main canvas
  8646,73:        console.log(`🎨 GRADIENT MASK [${callId}]: Using scale factor: ${scaleFactor}`);
  8651,42:        tempCanvas.width = logicalWidth * scaleFactor;
  8652,44:        tempCanvas.height = logicalHeight * scaleFactor;
  8656,22:        tempCtx.scale(scaleFactor, scaleFactor);
  8656,35:        tempCtx.scale(scaleFactor, scaleFactor);
  8669,42:        textCanvas.width = logicalWidth * scaleFactor;
  8670,44:        textCanvas.height = logicalHeight * scaleFactor;
  8674,22:        textCtx.scale(scaleFactor, scaleFactor);
  8674,35:        textCtx.scale(scaleFactor, scaleFactor);
  8698,65:        console.log(`🔍 GRID DISTORT GRADIENT DEBUG [${callId}]: scaleFactor:`, scaleFactor);
  8698,80:        console.log(`🔍 GRID DISTORT GRADIENT DEBUG [${callId}]: scaleFactor:`, scaleFactor);
  8760,63:        console.log(`🔍 GRID DISTORT SHADOW DEBUG [${callId}]: scaleFactor:`, scaleFactor);
  8760,78:        console.log(`🔍 GRID DISTORT SHADOW DEBUG [${callId}]: scaleFactor:`, scaleFactor);
  8798,89:                console.log(`🔍 GRID DISTORT SHADOW DEBUG [${callId}]: Quality setting - scaleFactor:`, scaleFactor);
  8798,104:                console.log(`🔍 GRID DISTORT SHADOW DEBUG [${callId}]: Quality setting - scaleFactor:`, scaleFactor);
  11911,18:        const tempScaleFactor = tempTransform.a;
  11912,33:        tempCtx.setTransform(tempScaleFactor, 0, 0, tempScaleFactor, 0, 0);
  11912,56:        tempCtx.setTransform(tempScaleFactor, 0, 0, tempScaleFactor, 0, 0);
  11913,55:        tempCtx.clearRect(0, 0, tempCanvas.width / tempScaleFactor, tempCanvas.height / tempScaleFactor);
  11913,92:        tempCtx.clearRect(0, 0, tempCanvas.width / tempScaleFactor, tempCanvas.height / tempScaleFactor);
  13301,32:                    const strokeScaleFactor = strokeTransform.a;
  13302,49:                    strokeCtx.setTransform(strokeScaleFactor, 0, 0, strokeScaleFactor, 0, 0);
  13302,74:                    strokeCtx.setTransform(strokeScaleFactor, 0, 0, strokeScaleFactor, 0, 0);
  13303,73:                    strokeCtx.clearRect(0, 0, strokeCanvas.width / strokeScaleFactor, strokeCanvas.height / strokeScaleFactor);
  13303,114:                    strokeCtx.clearRect(0, 0, strokeCanvas.width / strokeScaleFactor, strokeCanvas.height / strokeScaleFactor);
  13342,31:                    const colorScaleFactor = colorTransform.a;
  13343,47:                    colorCtx.setTransform(colorScaleFactor, 0, 0, colorScaleFactor, 0, 0);
  13343,71:                    colorCtx.setTransform(colorScaleFactor, 0, 0, colorScaleFactor, 0, 0);
  13344,70:                    colorCtx.clearRect(0, 0, colorCanvas.width / colorScaleFactor, colorCanvas.height / colorScaleFactor);
  13344,109:                    colorCtx.clearRect(0, 0, colorCanvas.width / colorScaleFactor, colorCanvas.height / colorScaleFactor);
  13756,44:            console.log(`🎨 [UPDATE] Canvas scaleFactor: ${canvas.scaleFactor}`);
  13756,66:            console.log(`🎨 [UPDATE] Canvas scaleFactor: ${canvas.scaleFactor}`);
  13762,20:        const canvasScaleFactor = canvas.scaleFactor || 1;
  13762,41:        const canvasScaleFactor = canvas.scaleFactor || 1;
  13763,31:        ctx.setTransform(canvasScaleFactor, 0, 0, canvasScaleFactor, 0, 0);
  13763,56:        ctx.setTransform(canvasScaleFactor, 0, 0, canvasScaleFactor, 0, 0);
  13766,48:        ctx.fillRect(0, 0, canvas.width / canvasScaleFactor, canvas.height / canvasScaleFactor);
  13766,83:        ctx.fillRect(0, 0, canvas.width / canvasScaleFactor, canvas.height / canvasScaleFactor);
  13767,52:        // ctx.clearRect(0, 0, canvas.width / canvasScaleFactor, canvas.height / canvasScaleFactor); // Original clear removed
  13767,87:        // ctx.clearRect(0, 0, canvas.width / canvasScaleFactor, canvas.height / canvasScaleFactor); // Original clear removed
  17167,26:                    const scaleFactor = Math.min(canvas.width / img.width, canvas.height / img.height, 1) * 0.8;
  17174,31:                        scale: scaleFactor,
  22692,20:        const exportScaleFactor = exportTransform.a;
  22693,37:        exportCtx.setTransform(exportScaleFactor, 0, 0, exportScaleFactor, 0, 0);
  22693,62:        exportCtx.setTransform(exportScaleFactor, 0, 0, exportScaleFactor, 0, 0);
  22694,61:        exportCtx.clearRect(0, 0, exportCanvas.width / exportScaleFactor, exportCanvas.height / exportScaleFactor);
  22694,102:        exportCtx.clearRect(0, 0, exportCanvas.width / exportScaleFactor, exportCanvas.height / exportScaleFactor);
  23619,30:    function setCanvasQuality(scaleFactor) {
  23620,67:        console.log(`🎯 [QUALITY] === CHANGING CANVAS QUALITY TO ${scaleFactor}x ===`);
  23621,43:        console.log(`🎯 [QUALITY] Previous scaleFactor: ${currentScaleFactor}`);
  23621,65:        console.log(`🎯 [QUALITY] Previous scaleFactor: ${currentScaleFactor}`);
  23623,15:        currentScaleFactor = scaleFactor;
  23623,29:        currentScaleFactor = scaleFactor;
  23630,54:        canvasScale = setupHighDPICanvas(canvas, ctx, scaleFactor);
  23638,34:        updateQualityButtonStates(scaleFactor);
  23641,29:        showQualityIndicator(scaleFactor);
  23651,61:        console.log(`✅ [QUALITY] Canvas quality changed to ${scaleFactor}x`);
  23657,34:    function showQualityIndicator(scaleFactor) {
  23681,51:        indicator.textContent = `Canvas Quality: ${scaleFactor}x`;
  23714,41:        updateQualityButtonStates(currentScaleFactor);

