<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Effect Capture Functions</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #3B82F6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #2563EB;
        }
        .result {
            margin-top: 15px;
            padding: 15px;
            border-radius: 6px;
            white-space: pre-wrap;
            font-family: monospace;
            font-size: 12px;
            max-height: 400px;
            overflow-y: auto;
        }
        .result.success {
            background: #D1FAE5;
            border: 1px solid #10B981;
            color: #065F46;
        }
        .result.error {
            background: #FEE2E2;
            border: 1px solid #EF4444;
            color: #991B1B;
        }
        .result.info {
            background: #DBEAFE;
            border: 1px solid #3B82F6;
            color: #1E40AF;
        }
        h1 {
            color: #1F2937;
            text-align: center;
        }
        h3 {
            color: #374151;
            margin-bottom: 10px;
        }
        .instructions {
            background: #FEF3C7;
            border: 1px solid #F59E0B;
            color: #92400E;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <h1>🧪 Effect Capture Functions Test</h1>
    
    <div class="instructions">
        <strong>Test Instructions:</strong><br>
        1. Open the design editor in another tab<br>
        2. Add an image and apply effects (duotone, glitch, CSS filters)<br>
        3. Use these tests to verify the capture functions work<br>
        4. Check console logs for detailed debugging information
    </div>

    <div class="test-section">
        <h3>Test 1: Check Function Availability</h3>
        <p>Verify that all capture and restore functions are available.</p>
        <button class="test-button" onclick="testFunctionAvailability()">Check Functions</button>
        <div id="functionsResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 2: Test CSS Filter Capture</h3>
        <p>Test capturing CSS filter state from the design editor.</p>
        <button class="test-button" onclick="testCSSFilterCapture()">Test CSS Filter Capture</button>
        <div id="cssResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 3: Test Duotone Capture</h3>
        <p>Test capturing duotone effect state from the design editor.</p>
        <button class="test-button" onclick="testDuotoneCapture()">Test Duotone Capture</button>
        <div id="duotoneResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 4: Test Glitch Capture</h3>
        <p>Test capturing glitch effect state from the design editor.</p>
        <button class="test-button" onclick="testGlitchCapture()">Test Glitch Capture</button>
        <div id="glitchResult" class="result"></div>
    </div>

    <div class="test-section">
        <h3>Test 5: Simulate Save Process</h3>
        <p>Simulate the complete save process with all effect captures.</p>
        <button class="test-button" onclick="simulateSaveProcess()">Simulate Save</button>
        <div id="saveResult" class="result"></div>
    </div>

    <script>
        function testFunctionAvailability() {
            const result = document.getElementById('functionsResult');
            
            const functions = [
                'captureCSSFilterState',
                'captureDuotoneState', 
                'captureGlitchState',
                'restoreCSSFilterState',
                'restoreDuotoneState',
                'restoreGlitchState'
            ];
            
            let report = 'Function Availability Report:\n\n';
            let allAvailable = true;
            
            functions.forEach(funcName => {
                const available = typeof window[funcName] === 'function';
                report += `${available ? '✅' : '❌'} ${funcName}: ${available ? 'Available' : 'Not Available'}\n`;
                if (!available) allAvailable = false;
            });
            
            report += `\nOverall Status: ${allAvailable ? '✅ All functions available' : '❌ Some functions missing'}`;
            
            if (allAvailable) {
                report += '\n\nTo test these functions:\n';
                report += '1. Open design editor: http://localhost:3006/design-editor.html\n';
                report += '2. Add an image and apply effects\n';
                report += '3. Run the capture tests below\n';
            }
            
            result.textContent = report;
            result.className = allAvailable ? 'result success' : 'result error';
        }

        function testCSSFilterCapture() {
            const result = document.getElementById('cssResult');
            
            if (typeof window.captureCSSFilterState !== 'function') {
                result.textContent = 'Error: captureCSSFilterState function not available';
                result.className = 'result error';
                return;
            }

            try {
                const cssFilterState = window.captureCSSFilterState();
                
                let report = 'CSS Filter State Captured:\n\n';
                report += JSON.stringify(cssFilterState, null, 2);
                report += '\n\nFunction executed successfully!';
                
                result.textContent = report;
                result.className = 'result success';
            } catch (error) {
                result.textContent = 'Error capturing CSS filter state: ' + error.message;
                result.className = 'result error';
            }
        }

        function testDuotoneCapture() {
            const result = document.getElementById('duotoneResult');
            
            if (typeof window.captureDuotoneState !== 'function') {
                result.textContent = 'Error: captureDuotoneState function not available';
                result.className = 'result error';
                return;
            }

            try {
                const duotoneState = window.captureDuotoneState();
                
                let report = 'Duotone State Captured:\n\n';
                report += JSON.stringify(duotoneState, null, 2);
                report += '\n\nFunction executed successfully!';
                
                result.textContent = report;
                result.className = 'result success';
            } catch (error) {
                result.textContent = 'Error capturing duotone state: ' + error.message;
                result.className = 'result error';
            }
        }

        function testGlitchCapture() {
            const result = document.getElementById('glitchResult');
            
            if (typeof window.captureGlitchState !== 'function') {
                result.textContent = 'Error: captureGlitchState function not available';
                result.className = 'result error';
                return;
            }

            try {
                const glitchState = window.captureGlitchState();
                
                let report = 'Glitch State Captured:\n\n';
                report += JSON.stringify(glitchState, null, 2);
                report += '\n\nFunction executed successfully!';
                
                result.textContent = report;
                result.className = 'result success';
            } catch (error) {
                result.textContent = 'Error capturing glitch state: ' + error.message;
                result.className = 'result error';
            }
        }

        function simulateSaveProcess() {
            const result = document.getElementById('saveResult');
            
            try {
                let report = 'Simulating Complete Save Process:\n\n';
                
                // Test all capture functions
                const cssFilterState = window.captureCSSFilterState ? window.captureCSSFilterState() : {};
                const duotoneState = window.captureDuotoneState ? window.captureDuotoneState() : {};
                const glitchState = window.captureGlitchState ? window.captureGlitchState() : {};
                
                const projectData = {
                    title: 'Test Project',
                    cssFilterState: cssFilterState,
                    duotoneState: duotoneState,
                    glitchState: glitchState,
                    adminData: {
                        cssFilterState: cssFilterState,
                        duotoneState: duotoneState,
                        glitchState: glitchState
                    }
                };
                
                report += 'Complete Project Data with Effects:\n';
                report += JSON.stringify(projectData, null, 2);
                report += '\n\n✅ Save simulation completed successfully!';
                report += '\n\nThis data would be sent to the server for saving.';
                
                result.textContent = report;
                result.className = 'result success';
            } catch (error) {
                result.textContent = 'Error in save simulation: ' + error.message;
                result.className = 'result error';
            }
        }
    </script>
</body>
</html>
